import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Card, Row, Col, Button, Space, Table, Tag, Modal } from 'antd';
import { ToolOutlined, PlusOutlined, ReloadOutlined, AppstoreOutlined, DeleteOutlined } from '@ant-design/icons';
import VersionManager from '../components/business/VersionManager';
import ProgressIndicator from '../components/common/ProgressIndicator';
import ErrorAlert from '../components/common/ErrorAlert';
import { useConfirmDialog, confirmDelete, confirmDangerous } from '../components/common/ConfirmDialog';
import { useOperation, useNotifications } from '../contexts/StatusBarContext';

const { Title, Paragraph } = Typography;

const Tools: React.FC = () => {
  const [versionManagerVisible, setVersionManagerVisible] = useState(false);
  const [selectedManager, setSelectedManager] = useState<string>('');
  const { showConfirm, ConfirmDialog } = useConfirmDialog();
  const { runOperation } = useOperation();
  const { notifyError, notifyWarning } = useNotifications();
  const [installProgress, setInstallProgress] = useState<{
    visible: boolean;
    progress: number;
    status: 'idle' | 'downloading' | 'installing' | 'configuring' | 'completed' | 'error';
    message?: string;
    details?: string[];
  }>({
    visible: false,
    progress: 0,
    status: 'idle'
  });
  const [error, setError] = useState<{
    visible: boolean;
    title: string;
    message: string;
    details?: string[];
    suggestions?: string[];
  } | null>(null);

  const handleManageVersions = (managerName: string) => {
    setSelectedManager(managerName);
    setVersionManagerVisible(true);
  };

  const handleInstall = async (managerName: string) => {
    setInstallProgress({
      visible: true,
      progress: 0,
      status: 'downloading',
      message: `正在安装 ${managerName}...`,
      details: ['准备下载安装包...']
    });

    try {
      await runOperation(`安装 ${managerName}`, async (updateProgress) => {
        // 模拟安装过程
        for (let i = 0; i <= 100; i += 10) {
          updateProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));

        let status: 'downloading' | 'installing' | 'configuring' | 'completed' = 'downloading';
        let message = `正在下载 ${managerName}...`;
        let details = [`下载进度: ${i}%`];

        if (i >= 30 && i < 70) {
          status = 'installing';
          message = `正在安装 ${managerName}...`;
          details = [`安装进度: ${i - 30}%`, '创建目录结构...', '复制文件...'];
        } else if (i >= 70 && i < 100) {
          status = 'configuring';
          message = `正在配置 ${managerName}...`;
          details = [`配置进度: ${i - 70}%`, '设置环境变量...', '创建配置文件...'];
        } else if (i === 100) {
          status = 'completed';
          message = `${managerName} 安装完成！`;
          details = ['安装成功', '环境变量已配置', '可以开始使用'];
        }

        setInstallProgress(prev => ({
          ...prev,
          progress: i,
          status,
          message,
          details
        }));
        }

        // 3秒后隐藏进度条
        setTimeout(() => {
          setInstallProgress(prev => ({ ...prev, visible: false }));
        }, 3000);
      });

    } catch (err) {
      setInstallProgress(prev => ({
        ...prev,
        status: 'error',
        message: `${managerName} 安装失败`,
        details: ['安装过程中发生错误']
      }));

      setError({
        visible: true,
        title: '安装失败',
        message: `无法安装 ${managerName}，请检查网络连接和系统权限。`,
        details: [
          '网络连接超时',
          '下载文件损坏',
          '权限不足'
        ],
        suggestions: [
          '检查网络连接是否正常',
          '以管理员身份运行程序',
          '尝试使用代理或镜像源',
          '检查防火墙设置'
        ]
      });

      notifyError(`安装 ${managerName} 失败`);
    }
  };

  const handleRetry = () => {
    if (selectedManager) {
      setError(null);
      handleInstall(selectedManager);
    }
  };

  const handleDismissError = () => {
    setError(null);
  };

  // 卸载工具的确认对话框
  const handleUninstall = async (toolName: string) => {
    const confirmed = await showConfirm(confirmDelete(
      `卸载 ${toolName}`,
      <>
        <p>您确定要卸载 <strong>{toolName}</strong> 吗？</p>
        <p>这将删除所有相关的配置文件和已安装的版本。</p>
      </>,
      {
        consequences: [
          '所有已安装的版本将被删除',
          '相关的配置文件将被清除',
          '环境变量设置将被移除',
          '此操作无法撤销'
        ]
      }
    ));

    if (confirmed) {
      // 执行卸载逻辑
      console.log(`卸载 ${toolName}`);
    }
  };

  // 重置配置的确认对话框
  const handleResetConfig = async (toolName: string) => {
    const confirmed = await showConfirm(confirmDangerous(
      `重置 ${toolName} 配置`,
      <>
        <p>您确定要重置 <strong>{toolName}</strong> 的所有配置吗？</p>
        <p>这将恢复到默认设置，所有自定义配置将丢失。</p>
      </>,
      'RESET',
      {
        consequences: [
          '所有自定义配置将丢失',
          '环境变量将重置为默认值',
          '可能需要重新配置项目环境'
        ]
      }
    ));

    if (confirmed) {
      // 执行重置逻辑
      console.log(`重置 ${toolName} 配置`);
    }
  };
  const columns = [
    {
      title: '工具名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === '已安装' ? 'success' : 'warning';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (version: string) => version || '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button size="small">检测</Button>
          {record.status === '未安装' ? (
            <Button
              size="small"
              type="primary"
              onClick={() => handleInstall(record.name)}
            >
              安装
            </Button>
          ) : (
            <>
              <Button
                size="small"
                icon={<AppstoreOutlined />}
                onClick={() => handleManageVersions(record.name)}
              >
                管理版本
              </Button>
              <Button
                size="small"
                onClick={() => handleResetConfig(record.name)}
              >
                重置配置
              </Button>
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleUninstall(record.name)}
              >
                卸载
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 模拟数据 - 这里应该从实际的检测结果获取
  const data = [
    {
      key: '1',
      name: 'SDKMan',
      description: 'Java生态系统的软件开发工具包管理器',
      status: '未安装',
      version: '-',
    },
    {
      key: '2',
      name: 'pyenv',
      description: 'Python版本管理工具',
      status: '未安装',
      version: '-',
    },
    {
      key: '3',
      name: 'nvm',
      description: 'Node.js版本管理工具',
      status: '未安装',
      version: '-',
    },
  ];

  return (
    <div style={{
      height: '100%',
      overflow: 'auto',
      padding: '0 8px' // 减少左右padding，为滚动条留出空间
    }}>

      <Card
        title={
          <Space>
            <ToolOutlined />
            <span>支持的工具</span>
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ReloadOutlined />}>
              刷新
            </Button>
            <Button type="primary" icon={<PlusOutlined />}>
              添加工具
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 安装进度指示器 */}
      {installProgress.visible && (
        <ProgressIndicator
          progress={installProgress.progress}
          status={installProgress.status}
          message={installProgress.message}
          details={installProgress.details}
        />
      )}

      {/* 错误提示 */}
      {error?.visible && (
        <ErrorAlert
          title={error.title}
          message={error.message}
          details={error.details}
          suggestions={error.suggestions}
          onRetry={handleRetry}
          onDismiss={handleDismissError}
        />
      )}

      {/* 版本管理器 */}
      <VersionManager
        visible={versionManagerVisible}
        managerName={selectedManager}
        onClose={() => setVersionManagerVisible(false)}
      />

      {/* 确认对话框 */}
      <ConfirmDialog />
    </div>
  );
};

export default Tools;
