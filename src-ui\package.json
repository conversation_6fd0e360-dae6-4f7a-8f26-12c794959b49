{"name": "env-manager-ui", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "jest", "lint": "eslint src --ext ts,tsx", "format": "prettier --write src"}, "dependencies": {"@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-opener": "^2", "antd": "^5.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.0.0", "zustand": "^4.0.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/jest": "^29.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "typescript": "~5.8.3", "vite": "^7.0.4"}}