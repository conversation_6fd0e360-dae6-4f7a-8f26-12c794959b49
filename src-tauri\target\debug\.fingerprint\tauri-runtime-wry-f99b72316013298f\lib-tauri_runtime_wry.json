{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"macos-private-api\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 18149558971744852487, "deps": [[376837177317575824, "softbuffer", false, 14278471563523806960], [2013030631243296465, "webview2_com", false, 9027294774060575009], [2172092324659420098, "tao", false, 13311193925533253042], [3722963349756955755, "once_cell", false, 8207665967349426210], [3899750328741010762, "build_script_build", false, 15779430798064537346], [4143744114649553716, "raw_window_handle", false, 17752533427338865920], [5986029879202738730, "log", false, 7881454566519146328], [9010263965687315507, "http", false, 13052604301562925712], [13076268252722892340, "url", false, 8042336736044679275], [14585479307175734061, "windows", false, 15438721831857517499], [15302940006583996851, "wry", false, 7509755889534043223], [17233053221795943287, "tauri_utils", false, 14638709583540406129], [18010483002580779355, "tauri_runtime", false, 16336778403848337715]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-f99b72316013298f\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}