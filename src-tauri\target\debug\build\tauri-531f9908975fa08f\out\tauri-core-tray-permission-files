["\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\tray\\autogenerated\\default.toml"]