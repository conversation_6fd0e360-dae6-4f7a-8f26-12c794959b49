G:\code\my-tool\env-manager\src-tauri\target\debug\deps\env_manager_lib.lib: ..\src\lib.rs ..\src\commands\mod.rs ..\src\commands\detection.rs ..\src\commands\installation.rs ..\src\commands\configuration.rs ..\src\commands\system.rs ..\src\commands\packages.rs ..\src\commands\version.rs ..\src\commands\logs.rs ..\src\commands\window.rs ..\src\models\mod.rs ..\src\models\config.rs ..\src\models\installation.rs ..\src\models\detection.rs ..\src\models\error.rs ..\src\utils\mod.rs ..\src\utils\process.rs ..\src\utils\safe_command.rs ..\src\utils\fs.rs ..\src\utils\logger.rs ..\src\utils\env.rs ..\src\utils\registry.rs ..\src\utils\download.rs ..\src\utils\validation.rs ..\src\config\mod.rs ..\src\traits\mod.rs G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

G:\code\my-tool\env-manager\src-tauri\target\debug\deps\env_manager_lib.dll: ..\src\lib.rs ..\src\commands\mod.rs ..\src\commands\detection.rs ..\src\commands\installation.rs ..\src\commands\configuration.rs ..\src\commands\system.rs ..\src\commands\packages.rs ..\src\commands\version.rs ..\src\commands\logs.rs ..\src\commands\window.rs ..\src\models\mod.rs ..\src\models\config.rs ..\src\models\installation.rs ..\src\models\detection.rs ..\src\models\error.rs ..\src\utils\mod.rs ..\src\utils\process.rs ..\src\utils\safe_command.rs ..\src\utils\fs.rs ..\src\utils\logger.rs ..\src\utils\env.rs ..\src\utils\registry.rs ..\src\utils\download.rs ..\src\utils\validation.rs ..\src\config\mod.rs ..\src\traits\mod.rs G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

G:\code\my-tool\env-manager\src-tauri\target\debug\deps\libenv_manager_lib.rlib: ..\src\lib.rs ..\src\commands\mod.rs ..\src\commands\detection.rs ..\src\commands\installation.rs ..\src\commands\configuration.rs ..\src\commands\system.rs ..\src\commands\packages.rs ..\src\commands\version.rs ..\src\commands\logs.rs ..\src\commands\window.rs ..\src\models\mod.rs ..\src\models\config.rs ..\src\models\installation.rs ..\src\models\detection.rs ..\src\models\error.rs ..\src\utils\mod.rs ..\src\utils\process.rs ..\src\utils\safe_command.rs ..\src\utils\fs.rs ..\src\utils\logger.rs ..\src\utils\env.rs ..\src\utils\registry.rs ..\src\utils\download.rs ..\src\utils\validation.rs ..\src\config\mod.rs ..\src\traits\mod.rs G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

G:\code\my-tool\env-manager\src-tauri\target\debug\deps\env_manager_lib.d: ..\src\lib.rs ..\src\commands\mod.rs ..\src\commands\detection.rs ..\src\commands\installation.rs ..\src\commands\configuration.rs ..\src\commands\system.rs ..\src\commands\packages.rs ..\src\commands\version.rs ..\src\commands\logs.rs ..\src\commands\window.rs ..\src\models\mod.rs ..\src\models\config.rs ..\src\models\installation.rs ..\src\models\detection.rs ..\src\models\error.rs ..\src\utils\mod.rs ..\src\utils\process.rs ..\src\utils\safe_command.rs ..\src\utils\fs.rs ..\src\utils\logger.rs ..\src\utils\env.rs ..\src\utils\registry.rs ..\src\utils\download.rs ..\src\utils\validation.rs ..\src\config\mod.rs ..\src\traits\mod.rs G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

..\src\lib.rs:
..\src\commands\mod.rs:
..\src\commands\detection.rs:
..\src\commands\installation.rs:
..\src\commands\configuration.rs:
..\src\commands\system.rs:
..\src\commands\packages.rs:
..\src\commands\version.rs:
..\src\commands\logs.rs:
..\src\commands\window.rs:
..\src\models\mod.rs:
..\src\models\config.rs:
..\src\models\installation.rs:
..\src\models\detection.rs:
..\src\models\error.rs:
..\src\utils\mod.rs:
..\src\utils\process.rs:
..\src\utils\safe_command.rs:
..\src\utils\fs.rs:
..\src\utils\logger.rs:
..\src\utils\env.rs:
..\src\utils\registry.rs:
..\src\utils\download.rs:
..\src\utils\validation.rs:
..\src\config\mod.rs:
..\src\traits\mod.rs:
G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# <AUTHOR> <EMAIL>
# env-dep:CARGO_PKG_DESCRIPTION=Windows环境管理工具
# env-dep:CARGO_PKG_NAME=env-manager
# env-dep:OUT_DIR=G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\env-manager-6ca0f627023d1827\\out
