use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// SDK 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdkInfo {
    pub name: String,           // SDK 名称 (java, python, nodejs, go)
    pub display_name: String,   // 显示名称
    pub description: String,    // 描述
    pub icon: String,          // 图标路径
    pub official_site: String, // 官方网站
    pub supported_platforms: Vec<String>, // 支持的平台
    pub category: String,      // 分类 (language, runtime, tool)
}

/// 版本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    pub version: String,        // 版本号
    pub release_date: String,   // 发布日期
    pub is_lts: bool,          // 是否为 LTS 版本
    pub is_latest: bool,       // 是否为最新版本
    pub download_url: String,   // 下载链接
    pub checksum: Option<String>, // 文件校验和
    pub size: Option<u64>,     // 文件大小
    pub release_notes: Option<String>, // 发布说明
    pub platform: String,     // 平台 (windows-x64, linux-x64, etc.)
}

/// 安装的版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstalledVersion {
    pub sdk: String,           // SDK 名称
    pub version: String,       // 版本号
    pub install_path: String,  // 安装路径
    pub is_active: bool,       // 是否为当前活跃版本
    pub install_date: String,  // 安装日期
    pub size: Option<u64>,     // 安装大小
}

/// SDK 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdkConfig {
    pub install_base_path: String,  // 基础安装路径
    pub mirror_urls: HashMap<String, String>, // 镜像源配置
    pub proxy_settings: Option<ProxyConfig>,  // 代理设置
    pub auto_set_env: bool,        // 自动设置环境变量
    pub backup_env: bool,          // 备份环境变量
}

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub enabled: bool,
    pub host: String,
    pub port: u16,
    pub username: Option<String>,
    pub password: Option<String>,
    pub proxy_type: ProxyType,
}

/// 代理类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProxyType {
    Http,
    Https,
    Socks5,
}

/// 下载进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadProgress {
    pub sdk: String,
    pub version: String,
    pub downloaded: u64,
    pub total: u64,
    pub speed: u64,           // bytes per second
    pub eta: Option<u64>,     // estimated time remaining in seconds
    pub status: DownloadStatus,
}

/// 下载状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

/// 安装配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstallConfig {
    pub install_path: Option<String>,
    pub set_as_default: bool,
    pub create_shortcuts: bool,
    pub add_to_path: bool,
    pub custom_options: HashMap<String, String>,
}

/// 环境变量操作
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvVarOperation {
    pub name: String,
    pub value: String,
    pub operation_type: EnvVarOperationType,
    pub scope: EnvVarScope,
}

/// 环境变量操作类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnvVarOperationType {
    Set,
    Append,
    Prepend,
    Remove,
}

/// 环境变量作用域
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnvVarScope {
    User,
    System,
    Process,
}

/// SDK 操作结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdkOperationResult {
    pub success: bool,
    pub message: String,
    pub details: Option<String>,
    pub error_code: Option<String>,
}

/// SDK 状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdkStatus {
    pub sdk: String,
    pub installed_versions: Vec<InstalledVersion>,
    pub active_version: Option<String>,
    pub available_versions: Vec<VersionInfo>,
    pub last_updated: String,
}
