[package]
name = "env-manager"
version = "1.0.0"
description = "Windows环境管理工具"
authors = ["Your Team <<EMAIL>>"]
edition = "2021"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "env-manager"
path = "../src/main.rs"

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "env_manager_lib"
path = "../src/lib.rs"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-window-state = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
anyhow = "1.0"
dirs = "6.0"
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
thiserror = "1.0"
toml = "0.8"
regex = "1.10"
chrono = { version = "0.4", features = ["serde"] }
zip = "0.6"
flate2 = "1.0"
futures-util = "0.3"
md5 = "0.7"
sha2 = "0.10"
async-trait = "0.1"
once_cell = "1.19"
sha256 = "1.4"
version-compare = "0.1"
