use std::collections::HashMap;
use std::path::Path;
use anyhow::{Result, anyhow};

use super::base::{PackageManager, InstallationInfo, InstallationStatus, InstallConfig, Settings};
use crate::utils::safe_command::{execute_command_safe, SafeCommandConfig};
use crate::utils::fs::check_path_exists;

/// nvm-windows 包管理器实现
pub struct NvmManager {
    name: String,
    description: String,
}

impl NvmManager {
    /// 创建新的 nvm 管理器实例
    pub fn new() -> Self {
        Self {
            name: "nvm".to_string(),
            description: "Node.js版本管理工具".to_string(),
        }
    }
    
    /// 获取 nvm 安装目录
    fn get_nvm_dir(&self) -> Option<String> {
        // 检查环境变量
        if let Ok(dir) = std::env::var("NVM_HOME") {
            return Some(dir);
        }
        
        if let Ok(dir) = std::env::var("NVM_DIR") {
            return Some(dir);
        }
        
        // 检查默认安装路径
        if cfg!(target_os = "windows") {
            // Windows 默认路径
            if let Ok(program_files) = std::env::var("PROGRAMFILES") {
                let nvm_path = Path::new(&program_files).join("nodejs").join("nvm");
                if nvm_path.exists() {
                    return Some(nvm_path.to_string_lossy().to_string());
                }
            }
            
            // AppData 路径
            if let Some(home) = dirs::home_dir() {
                let appdata_path = home.join("AppData").join("Roaming").join("nvm");
                if appdata_path.exists() {
                    return Some(appdata_path.to_string_lossy().to_string());
                }
            }
        } else {
            // Unix 系统默认路径
            if let Some(home) = dirs::home_dir() {
                let default_path = home.join(".nvm");
                if default_path.exists() {
                    return Some(default_path.to_string_lossy().to_string());
                }
            }
        }
        
        None
    }
    
    /// 检查 nvm 命令是否可用
    fn check_command_available(&self) -> bool {
        // 避免执行命令，改为检查文件存在性
        if let Ok(nvm_home) = std::env::var("NVM_HOME") {
            let nvm_exe = format!("{}/nvm.exe", nvm_home);
            std::path::Path::new(&nvm_exe).exists()
        } else {
            false
        }
    }
    
    /// 获取 nvm 版本
    fn get_nvm_version(&self) -> Result<String> {
        let config = SafeCommandConfig::default();
        execute_command_safe("nvm", &["version"], config)
    }
    
    /// 检查环境变量配置
    fn check_env_configured(&self) -> bool {
        std::env::var("NVM_HOME").is_ok() || 
        std::env::var("NVM_DIR").is_ok() ||
        std::env::var("PATH").unwrap_or_default().contains("nvm")
    }
    
    /// 获取已安装的 Node.js 版本列表
    fn get_installed_versions(&self) -> Result<Vec<String>> {
        let config = SafeCommandConfig::default();
        let output = execute_command_safe("nvm", &["list"], config)?;

        let mut versions = Vec::new();
        for line in output.lines() {
            let line = line.trim();
            if line.starts_with("v") || line.chars().next().unwrap_or(' ').is_ascii_digit() {
                // 移除前导的 "* " 标记和空格
                let version = line.trim_start_matches("* ").trim();
                if !version.is_empty() && version != "No installations recognized." {
                    versions.push(version.to_string());
                }
            }
        }

        Ok(versions)
    }
    
    /// 获取当前活动的 Node.js 版本
    fn get_current_version(&self) -> Result<String> {
        let config = SafeCommandConfig::default();
        execute_command_safe("nvm", &["current"], config)
    }
    
    /// 获取可用的 Node.js 版本列表（在线）
    fn get_available_versions(&self) -> Result<Vec<String>> {
        let config = SafeCommandConfig::default();
        let output = execute_command_safe("nvm", &["list", "available"], config)?;

        let mut versions = Vec::new();
        for line in output.lines() {
            let line = line.trim();
            if line.starts_with("v") && line.len() > 1 {
                versions.push(line.to_string());
            }
        }

        // 如果没有获取到在线版本，返回默认列表
        if versions.is_empty() {
            versions = vec![
                "v21.1.0".to_string(),
                "v20.9.0".to_string(),
                "v18.18.2".to_string(),
                "v16.20.2".to_string(),
                "v14.21.3".to_string(),
            ];
        }

        Ok(versions)
    }
}

impl PackageManager for NvmManager {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
    
    fn detect_installation(&self) -> Result<InstallationInfo> {
        let mut info = InstallationInfo {
            name: self.name.clone(),
            status: InstallationStatus::NotInstalled,
            version: None,
            install_path: None,
            env_configured: false,
            metadata: HashMap::new(),
        };
        
        // 检查安装目录
        if let Some(install_path) = self.get_nvm_dir() {
            info.install_path = Some(install_path);
            info.status = InstallationStatus::Installed;
            
            // 检查命令是否可用
            if self.check_command_available() {
                // 获取版本信息
                if let Ok(version) = self.get_nvm_version() {
                    info.version = Some(version);
                }
                
                // 检查环境变量配置
                info.env_configured = self.check_env_configured();
                
                // 获取已安装的 Node.js 版本
                if let Ok(versions) = self.get_installed_versions() {
                    info.metadata.insert("installed_versions".to_string(), versions.join(","));
                    info.metadata.insert("version_count".to_string(), versions.len().to_string());
                }
                
                // 获取当前活动版本
                if let Ok(current) = self.get_current_version() {
                    info.metadata.insert("current_version".to_string(), current);
                }
            } else {
                info.status = InstallationStatus::Failed;
                info.metadata.insert("error".to_string(), "命令不可用".to_string());
            }
        }
        
        Ok(info)
    }
    
    fn install(&self, config: InstallConfig) -> Result<()> {
        if cfg!(target_os = "windows") {
            // Windows 上安装 nvm-windows
            let install_url = "https://github.com/coreybutler/nvm-windows/releases/latest/download/nvm-setup.exe";
            
            // 下载安装程序
            let output = execute_command("powershell", &[
                "-Command",
                &format!("Invoke-WebRequest -Uri '{}' -OutFile 'nvm-setup.exe'", install_url)
            ])?;
            
            // 运行安装程序（需要用户交互）
            println!("请运行 nvm-setup.exe 完成安装");
            
        } else {
            // Unix 系统安装脚本
            let install_script = r#"
                curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
            "#;
            
            execute_command("bash", &["-c", install_script])?;
        }
        
        // 如果指定了自定义安装路径
        if let Some(custom_path) = config.install_path {
            if cfg!(target_os = "windows") {
                std::env::set_var("NVM_HOME", custom_path);
            } else {
                std::env::set_var("NVM_DIR", custom_path);
            }
        }
        
        Ok(())
    }
    
    fn uninstall(&self) -> Result<()> {
        if let Some(nvm_dir) = self.get_nvm_dir() {
            // 删除 nvm 目录
            std::fs::remove_dir_all(&nvm_dir)?;
            
            // 清理环境变量
            if cfg!(target_os = "windows") {
                println!("请手动从系统环境变量中移除 NVM_HOME");
            } else {
                println!("请手动从系统环境变量中移除 NVM_DIR");
            }
        }
        
        Ok(())
    }
    
    fn get_version(&self) -> Result<String> {
        self.get_nvm_version()
    }
    
    fn configure(&self, settings: Settings) -> Result<()> {
        // nvm 的配置主要通过环境变量和配置文件
        for (key, value) in settings.config {
            match key.as_str() {
                "NVM_HOME" | "NVM_DIR" => {
                    std::env::set_var(&key, value);
                }
                "default_version" => {
                    // 对于设置默认版本，我们可以安全地执行
                    println!("设置默认版本: {}", value);
                    let config = SafeCommandConfig {
                        allow_popup: true, // 允许弹窗，因为这是用户主动操作
                        ..Default::default()
                    };

                    match execute_command_safe("nvm", &["alias", "default", &value], config) {
                        Ok(_) => println!("默认版本设置成功"),
                        Err(e) => println!("设置默认版本失败: {}", e),
                    }
                }
                _ => {
                    // 其他配置可以写入 nvm 配置文件
                    if let Some(nvm_dir) = self.get_nvm_dir() {
                        let config_file = Path::new(&nvm_dir).join("settings.txt");
                        let config_line = format!("{}={}\n", key, value);
                        
                        // 追加到配置文件
                        std::fs::write(&config_file, config_line)?;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    fn is_supported(&self) -> bool {
        // nvm 支持 Windows (nvm-windows) 和 Unix 系统
        true
    }
    
    fn get_dependencies(&self) -> Vec<String> {
        if cfg!(target_os = "windows") {
            vec![
                "PowerShell".to_string(),
            ]
        } else {
            vec![
                "curl".to_string(),
                "bash".to_string(),
            ]
        }
    }
    
    fn validate_installation(&self) -> Result<bool> {
        let info = self.detect_installation()?;
        Ok(info.status == InstallationStatus::Installed && info.env_configured)
    }
}

impl Default for NvmManager {
    fn default() -> Self {
        Self::new()
    }
}
