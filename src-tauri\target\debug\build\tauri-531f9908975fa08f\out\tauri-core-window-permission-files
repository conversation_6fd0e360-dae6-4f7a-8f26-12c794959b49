["\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_focusable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_simple_fullscreen.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\window\\autogenerated\\default.toml"]