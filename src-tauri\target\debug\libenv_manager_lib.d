G:\code\my-tool\env-manager\src-tauri\target\debug\libenv_manager_lib.rlib: G:\code\my-tool\env-manager\src-tauri\..\src\commands\configuration.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\detection.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\installation.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\logs.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\mod.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\packages.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\system.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\version.rs G:\code\my-tool\env-manager\src-tauri\..\src\commands\window.rs G:\code\my-tool\env-manager\src-tauri\..\src\config\mod.rs G:\code\my-tool\env-manager\src-tauri\..\src\lib.rs G:\code\my-tool\env-manager\src-tauri\..\src\models\config.rs G:\code\my-tool\env-manager\src-tauri\..\src\models\detection.rs G:\code\my-tool\env-manager\src-tauri\..\src\models\error.rs G:\code\my-tool\env-manager\src-tauri\..\src\models\installation.rs G:\code\my-tool\env-manager\src-tauri\..\src\models\mod.rs G:\code\my-tool\env-manager\src-tauri\..\src\traits\mod.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\download.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\env.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\fs.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\logger.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\mod.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\process.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\registry.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\safe_command.rs G:\code\my-tool\env-manager\src-tauri\..\src\utils\validation.rs G:\code\my-tool\env-manager\src-tauri\build.rs G:\code\my-tool\env-manager\src-tauri\capabilities G:\code\my-tool\env-manager\src-tauri\target\debug\build\env-manager-6ca0f627023d1827\out\524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 G:\code\my-tool\env-manager\src-tauri\tauri.conf.json
