import React, { useState } from 'react';
import { Layout, Menu, Button, Typo<PERSON>, Space, Tooltip, message } from 'antd';
import {
  DashboardOutlined,
  ToolOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  AppstoreOutlined,
  CodeOutlined,
  NodeIndexOutlined,
  BugOutlined,
  FileTextOutlined,

  ConsoleSqlOutlined,
  ApiOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import SimpleStatusBar from '../common/SimpleStatusBar';
import DevConsole from '../common/DevConsole';
import { useStatusBarContext } from '../../contexts/StatusBarContext';

const { Sider, Content, Header } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [showDevConsole, setShowDevConsole] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 状态栏管理
  const statusBarContext = useStatusBarContext();

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/tools',
      icon: <ToolOutlined />,
      label: '工具管理',
    },
    {
      key: 'package-managers',
      icon: <AppstoreOutlined />,
      label: '包管理工具',
      children: [
        {
          key: '/packages/java',
          icon: <CodeOutlined />,
          label: 'Java',
        },
        {
          key: '/packages/python',
          icon: <BugOutlined />,
          label: 'Python',
        },
        {
          key: '/packages/nodejs',
          icon: <NodeIndexOutlined />,
          label: 'Node.js',
        },
      ],
    },
    {
      key: '/logs',
      icon: <FileTextOutlined />,
      label: '系统日志',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      key: '/about',
      icon: <InfoCircleOutlined />,
      label: '关于',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };





  const handleToggleDevConsole = () => {
    setShowDevConsole(!showDevConsole);
    message.info(showDevConsole ? '开发者控制台已关闭' : '开发者控制台已打开');
  };

  // 全局F5键支持
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F5') {
        event.preventDefault();
        console.log('全局F5键: 重新加载应用');
        window.location.reload();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between'
        }}>
          {!collapsed ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ApiOutlined style={{ fontSize: '24px', color: '#1677FF' }} />
              <Title level={4} style={{ margin: 0, color: '#1677FF' }}>
                环境管理
              </Title>
            </div>
          ) : (
            <ApiOutlined style={{ fontSize: '24px', color: '#1677FF' }} />
          )}
          {!collapsed && (
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '14px',
                width: 32,
                height: 32,
              }}
            />
          )}
        </div>

        {/* 收起状态下的展开按钮 */}
        {collapsed && (
          <div style={{
            position: 'absolute',
            top: '16px',
            right: '-12px',
            zIndex: 1000
          }}>
            <Button
              type="primary"
              size="small"
              icon={<MenuUnfoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
          </div>
        )}
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>
      <Layout>
        <Header style={{
          background: '#fff',
          padding: '0 16px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          height: 48,
        }}>
          <Space>
            <Tooltip title="开发者控制台">
              <Button
                type="text"
                icon={<ConsoleSqlOutlined />}
                onClick={handleToggleDevConsole}
                size="small"
              >
                控制台
              </Button>
            </Tooltip>

          </Space>
        </Header>
        <Content style={{
          padding: '16px 24px', // 减少上下padding
          background: '#f5f5f5',
          height: 'calc(100vh - 80px)', // 固定高度，减去Header和状态栏高度
          overflow: 'hidden', // 让子组件处理滚动
          paddingBottom: 56, // 为状态栏留出空间
        }}>
          {children}
        </Content>
      </Layout>

      {/* 状态栏 */}
      <SimpleStatusBar
        systemStatus={statusBarContext.systemStatus}
        currentOperation={statusBarContext.currentOperation}
      />

      {/* 开发者控制台 */}
      <DevConsole
        visible={showDevConsole}
        onClose={() => setShowDevConsole(false)}
        width={600}
      />
    </Layout>
  );
};

export default MainLayout;
