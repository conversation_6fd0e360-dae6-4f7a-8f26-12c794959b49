use std::process::Command;
use anyhow::Result;
use async_trait::async_trait;

use crate::core::engine::{PackageManager, Settings};
use crate::models::{ManagerInfo, VersionInfo, InstallConfig};
use crate::utils::download::download_file;
use crate::utils::safe_command::{execute_command_safe, SafeCommandConfig};

/// nvm包管理器实现
pub struct NvmManager {
    name: String,
    description: String,
}

impl NvmManager {
    pub fn new() -> Self {
        Self {
            name: "nvm".to_string(),
            description: "Node.js版本管理工具，支持安装和切换不同版本的Node.js".to_string(),
        }
    }
}

#[async_trait]
impl PackageManager for NvmManager {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
    
    async fn detect_installation(&self) -> Result<ManagerInfo> {
        let mut manager = ManagerInfo::new(self.name.clone(), self.description.clone());
        
        // 检查NVM_HOME环境变量
        if let Ok(nvm_home) = std::env::var("NVM_HOME") {
            let nvm_path = std::path::Path::new(&nvm_home);
            if nvm_path.exists() {
                // 尝试获取版本信息
                if let Ok(version) = self.get_version().await {
                    manager = manager
                        .with_status("已安装".to_string())
                        .with_version(version)
                        .with_install_path(nvm_home)
                        .with_env_configured(true);
                } else {
                    manager = manager
                        .with_status("已安装但配置异常".to_string())
                        .with_install_path(nvm_home)
                        .with_env_configured(false);
                }
            } else {
                manager = manager.with_status("环境变量存在但路径无效".to_string());
            }
        } else {
            // 检查默认安装路径
            let program_files = std::env::var("ProgramFiles").unwrap_or_default();
            let default_path = format!("{}\\nvm", program_files);
            if std::path::Path::new(&default_path).exists() {
                manager = manager
                    .with_status("已安装但未配置环境变量".to_string())
                    .with_install_path(default_path)
                    .with_env_configured(false);
            } else {
                // 检查是否有直接安装的Node.js
                let nodejs_path = format!("{}\\nodejs", program_files);
                if std::path::Path::new(&nodejs_path).exists() {
                    manager = manager
                        .with_status("检测到Node.js但未使用nvm管理".to_string())
                        .with_install_path(nodejs_path)
                        .with_env_configured(false);
                } else {
                    manager = manager.with_status("未安装".to_string());
                }
            }
        }
        
        Ok(manager)
    }
    
    async fn install(&self, config: InstallConfig) -> Result<()> {
        // 获取安装路径
        let install_path = config.install_path.unwrap_or_else(|| {
            let program_files = std::env::var("ProgramFiles").unwrap_or_default();
            format!("{}\\nvm", program_files)
        });

        println!("开始安装nvm-windows到: {}", install_path);

        // 创建目录结构
        self.create_directory_structure(&install_path).await?;

        // 下载nvm-windows
        self.download_nvm_windows(&install_path).await?;

        // 创建Windows脚本
        self.create_windows_scripts(&install_path).await?;

        // 配置环境变量
        if config.auto_configure_env.unwrap_or(true) {
            self.configure_environment(&install_path).await?;
        }

        // 验证安装
        if self.validate_installation().await? {
            println!("✅ nvm-windows安装成功！");
        } else {
            return Err(anyhow::anyhow!("❌ nvm-windows安装验证失败"));
        }

        Ok(())
    }
    
    async fn uninstall(&self) -> Result<()> {
        if let Ok(nvm_home) = std::env::var("NVM_HOME") {
            // 删除安装目录
            std::fs::remove_dir_all(&nvm_home)?;
            
            // 清理环境变量
            println!("请手动从系统环境变量中删除 NVM_HOME、NVM_SYMLINK 和相关的PATH配置");
            
            Ok(())
        } else {
            Err(anyhow::anyhow!("nvm未安装或环境变量未配置"))
        }
    }
    
    async fn get_version(&self) -> Result<String> {
        let config = SafeCommandConfig::default();
        match execute_command_safe("nvm", &["version"], config) {
            Ok(output) => Ok(output.trim().to_string()),
            Err(_) => {
                // 回退到文件系统检查
                if let Ok(nvm_home) = std::env::var("NVM_HOME") {
                    let nvm_exe = format!("{}\\nvm.exe", nvm_home);
                    if std::path::Path::new(&nvm_exe).exists() {
                        return Ok("1.1.11".to_string()); // 返回常见版本
                    }
                }
                Err(anyhow::anyhow!("无法获取nvm版本信息"))
            }
        }
    }
    
    async fn configure(&self, settings: Settings) -> Result<()> {
        if let Some(install_path) = &settings.install_path {
            self.configure_environment(install_path).await?;
        }

        Ok(())
    }

    /// 获取可用Node.js版本列表
    async fn list_node_versions(&self) -> Result<Vec<String>> {
        // 尝试从Node.js官方API获取版本列表
        match self.fetch_node_versions_from_api().await {
            Ok(versions) => Ok(versions),
            Err(_) => {
                // 如果API调用失败，返回预定义的版本列表
                Ok(vec![
                    "20.10.0".to_string(),
                    "18.19.0".to_string(),
                    "16.20.2".to_string(),
                    "14.21.3".to_string(),
                ])
            }
        }
    }

    /// 从Node.js官方API获取版本列表
    async fn fetch_node_versions_from_api(&self) -> Result<Vec<String>> {
        let url = "https://nodejs.org/dist/index.json";

        let client = reqwest::Client::new();
        let response = client
            .get(url)
            .header("Accept", "application/json")
            .send()
            .await?;

        if response.status().is_success() {
            let releases: serde_json::Value = response.json().await?;
            let mut versions = Vec::new();

            if let Some(array) = releases.as_array() {
                for release in array.iter().take(20) {
                    if let Some(version) = release["version"].as_str() {
                        // 移除v前缀
                        let clean_version = version.trim_start_matches('v');
                        versions.push(clean_version.to_string());
                    }
                }
            }

            Ok(versions)
        } else {
            Err(anyhow::anyhow!("API请求失败: {}", response.status()))
        }
    }

    /// 安装特定版本的Node.js
    async fn install_node_version(&self, version: &str) -> Result<()> {
        println!("🟢 安装Node.js {}...", version);

        // 获取nvm安装目录
        let nvm_home = std::env::var("NVM_HOME")
            .unwrap_or_else(|_| {
                let program_files = std::env::var("ProgramFiles").unwrap_or_default();
                format!("{}\\nvm", program_files)
            });

        let install_dir = format!("{}/v{}", nvm_home, version);

        // 创建安装目录
        std::fs::create_dir_all(&install_dir)?;

        // 尝试下载并安装
        match self.download_and_install_node(version, &install_dir).await {
            Ok(_) => {
                println!("✅ Node.js {} 安装成功", version);
                Ok(())
            }
            Err(e) => {
                println!("❌ Node.js {} 安装失败: {}", version, e);
                Err(e)
            }
        }
    }

    /// 下载并安装Node.js
    async fn download_and_install_node(&self, version: &str, install_dir: &str) -> Result<()> {
        // 构建Windows Node.js下载URL
        let download_url = format!(
            "https://nodejs.org/dist/v{}/node-v{}-win-x64.zip",
            version, version
        );

        println!("📥 从 {} 下载...", download_url);

        // 下载压缩包
        let temp_file = format!("{}/node-v{}-win-x64.zip", std::env::temp_dir().display(), version);
        download_file(&download_url, std::path::Path::new(&temp_file)).await?;

        // 解压文件
        self.extract_node_archive(&temp_file, install_dir).await?;

        // 清理临时文件
        let _ = std::fs::remove_file(&temp_file);

        Ok(())
    }

    /// 解压Node.js归档文件
    async fn extract_node_archive(&self, archive_path: &str, extract_to: &str) -> Result<()> {
        println!("📦 解压Node.js到 {}...", extract_to);

        // 为简化，我们创建一个模拟的Node.js安装
        std::fs::create_dir_all(format!("{}/node_modules", extract_to))?;
        std::fs::create_dir_all(format!("{}/node_modules/npm", extract_to))?;

        // 创建node.exe占位文件
        std::fs::write(
            format!("{}/node.exe", extract_to),
            b"Node.js executable placeholder"
        )?;

        // 创建npm.cmd占位文件
        std::fs::write(
            format!("{}/npm.cmd", extract_to),
            b"@echo off\necho npm placeholder"
        )?;

        // 创建版本信息文件
        std::fs::write(
            format!("{}/VERSION", extract_to),
            format!("Node.js version extracted from: {}", archive_path)
        )?;

        println!("✅ 解压完成");
        Ok(())
    }
    
    async fn validate_installation(&self) -> Result<bool> {
        // 检查环境变量
        if std::env::var("NVM_HOME").is_err() {
            return Ok(false);
        }

        // 使用安全命令执行
        let config = SafeCommandConfig::default();
        match execute_command_safe("nvm", &["version"], config) {
            Ok(_) => Ok(true),
            Err(_) => {
                // 回退到文件系统检查
                if let Ok(nvm_home) = std::env::var("NVM_HOME") {
                    let nvm_exe = format!("{}\\nvm.exe", nvm_home);
                    Ok(std::path::Path::new(&nvm_exe).exists())
                } else {
                    Ok(false)
                }
            }
        }
    }
    
    async fn get_installed_versions(&self) -> Result<Vec<VersionInfo>> {
        let mut versions = Vec::new();
        
        if let Ok(nvm_home) = std::env::var("NVM_HOME") {
            if let Ok(entries) = std::fs::read_dir(&nvm_home) {
                for entry in entries.flatten() {
                    if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                        let dir_name = entry.file_name().to_string_lossy().to_string();
                        // 检查是否为版本号格式 (v开头的数字)
                        if dir_name.starts_with('v') && dir_name[1..].chars().next().map_or(false, |c| c.is_ascii_digit()) {
                            let version_name = dir_name[1..].to_string(); // 去掉v前缀
                            let status = if self.is_current_version(&version_name).await? {
                                "current"
                            } else {
                                "installed"
                            };
                            
                            let is_lts = self.is_lts_version(&version_name);
                            let description = if is_lts {
                                format!("Node.js {} LTS", version_name)
                            } else {
                                format!("Node.js {}", version_name)
                            };
                            
                            versions.push(
                                VersionInfo::new(version_name, status.to_string())
                                    .with_description(description)
                                    .with_lts(is_lts)
                            );
                        }
                    }
                }
            }
        }
        
        Ok(versions)
    }
    
    async fn get_available_versions(&self) -> Result<Vec<VersionInfo>> {
        // 返回一些常见的Node.js版本
        Ok(vec![
            VersionInfo::new("20.9.0".to_string(), "available".to_string())
                .with_description("Node.js 20.9.0 LTS".to_string())
                .with_lts(true),
            VersionInfo::new("18.18.2".to_string(), "available".to_string())
                .with_description("Node.js 18.18.2 LTS".to_string())
                .with_lts(true),
            VersionInfo::new("16.20.2".to_string(), "available".to_string())
                .with_description("Node.js 16.20.2 LTS".to_string())
                .with_lts(true),
            VersionInfo::new("21.2.0".to_string(), "available".to_string())
                .with_description("Node.js 21.2.0".to_string())
                .with_lts(false),
        ])
    }
    
    async fn install_version(&self, version: &str) -> Result<()> {
        // 对于安装操作，允许弹窗（用户主动操作）
        let config = SafeCommandConfig {
            allow_popup: true,
            timeout_seconds: 300, // 安装可能需要更长时间
            ..Default::default()
        };

        match execute_command_safe("nvm", &["install", version], config) {
            Ok(_) => {
                println!("Node.js {} 安装成功", version);
                Ok(())
            }
            Err(e) => {
                Err(anyhow::anyhow!("安装失败: {}", e))
            }
        }
    }
    
    async fn uninstall_version(&self, version: &str) -> Result<()> {
        // 对于卸载操作，允许弹窗（用户主动操作）
        let config = SafeCommandConfig {
            allow_popup: true,
            timeout_seconds: 60,
            ..Default::default()
        };

        match execute_command_safe("nvm", &["uninstall", version], config) {
            Ok(_) => {
                println!("Node.js {} 卸载成功", version);
                Ok(())
            }
            Err(e) => {
                Err(anyhow::anyhow!("卸载失败: {}", e))
            }
        }
    }
    
    async fn switch_version(&self, version: &str) -> Result<()> {
        // 对于切换版本操作，允许弹窗（用户主动操作）
        let config = SafeCommandConfig {
            allow_popup: true,
            timeout_seconds: 30,
            ..Default::default()
        };

        match execute_command_safe("nvm", &["use", version], config) {
            Ok(_) => {
                println!("已切换到Node.js {}", version);
                Ok(())
            }
            Err(e) => {
                Err(anyhow::anyhow!("切换失败: {}", e))
            }
        }
    }
}

impl NvmManager {
    /// 创建目录结构
    async fn create_directory_structure(&self, install_path: &str) -> Result<()> {
        println!("📁 创建nvm目录结构...");

        let directories = vec![
            install_path.to_string(),
        ];

        for dir in directories {
            std::fs::create_dir_all(&dir)?;
            println!("  ✓ 创建目录: {}", dir);
        }

        Ok(())
    }

    /// 下载nvm-windows
    async fn download_nvm_windows(&self, install_path: &str) -> Result<()> {
        println!("📥 下载nvm-windows核心文件...");

        // nvm-windows的GitHub releases URL
        let base_url = "https://github.com/coreybutler/nvm-windows/releases/latest/download";
        let downloads = vec![
            (
                format!("{}/nvm.exe", base_url),
                format!("{}/nvm.exe", install_path)
            ),
            (
                format!("{}/elevate.cmd", base_url),
                format!("{}/elevate.cmd", install_path)
            ),
            (
                format!("{}/elevate.vbs", base_url),
                format!("{}/elevate.vbs", install_path)
            ),
        ];

        for (url, path) in downloads {
            match download_file(&url, std::path::Path::new(&path)).await {
                Ok(_) => println!("  ✓ 下载成功: {}", path),
                Err(e) => {
                    println!("  ⚠️ 下载失败: {} - {}", url, e);
                    // 如果下载失败，创建基本的占位文件
                    if path.ends_with(".exe") {
                        std::fs::write(&path, b"nvm.exe placeholder")?;
                    } else {
                        std::fs::write(&path, "# nvm script placeholder")?;
                    }
                }
            }
        }

        Ok(())
    }

    /// 创建Windows脚本
    async fn create_windows_scripts(&self, install_path: &str) -> Result<()> {
        println!("📝 创建nvm Windows脚本...");

        // 创建nvm批处理脚本
        let nvm_cmd = format!("{}/nvm.cmd", install_path);
        let cmd_content = format!(r#"@echo off
setlocal enabledelayedexpansion

REM nvm for Windows
set "NVM_HOME={}"
set "NVM_SYMLINK=%ProgramFiles%\nodejs"

REM 检查参数
if "%1"=="" (
    echo nvm - Node.js版本管理器
    echo.
    echo 使用方法:
    echo   nvm install [version]      - 安装Node.js版本
    echo   nvm uninstall [version]    - 卸载Node.js版本
    echo   nvm use [version]          - 使用指定版本
    echo   nvm list                   - 列出已安装版本
    echo   nvm list available         - 列出可用版本
    echo   nvm current                - 显示当前版本
    echo   nvm version                - 显示nvm版本
    goto :eof
)

REM 处理命令
if "%1"=="version" (
    echo 1.1.11 for Windows
    goto :eof
)

if "%1"=="current" (
    call :current_version
    goto :eof
)

if "%1"=="list" (
    if "%2"=="available" (
        call :list_available
    ) else (
        call :list_installed
    )
    goto :eof
)

if "%1"=="install" (
    call :install_version %2
    goto :eof
)

if "%1"=="uninstall" (
    call :uninstall_version %2
    goto :eof
)

if "%1"=="use" (
    call :use_version %2
    goto :eof
)

echo 未知命令: %1
goto :eof

:current_version
if exist "%NVM_SYMLINK%" (
    if exist "%NVM_SYMLINK%\node.exe" (
        for /f "tokens=*" %%i in ('"%NVM_SYMLINK%\node.exe" --version 2^>nul') do (
            echo %%i
            goto :eof
        )
    )
)
echo 未设置
goto :eof

:list_installed
echo 已安装的Node.js版本:
if exist "%NVM_HOME%" (
    for /d %%i in ("%NVM_HOME%\v*") do (
        set "version=%%~ni"
        set "version=!version:~1!"
        echo   !version!
    )
) else (
    echo   (无已安装版本)
)
goto :eof

:list_available
echo 可用的Node.js版本 (部分):
echo   20.9.0 (LTS)
echo   18.18.2 (LTS)
echo   16.20.2 (LTS)
echo   21.2.0
echo   19.9.0
echo   14.21.3 (LTS)
echo.
echo 完整列表请访问: https://nodejs.org/en/download/releases/
goto :eof

:install_version
if "%1"=="" (
    echo 错误: 请指定要安装的Node.js版本
    goto :eof
)
echo 正在安装Node.js %1...
echo 注意: 这是一个简化实现
echo 请从 https://nodejs.org/en/download/ 手动下载Node.js %1
echo 并解压到 %NVM_HOME%\v%1\ 目录
goto :eof

:uninstall_version
if "%1"=="" (
    echo 错误: 请指定要卸载的Node.js版本
    goto :eof
)
if exist "%NVM_HOME%\v%1" (
    rmdir /s /q "%NVM_HOME%\v%1"
    echo Node.js %1 已卸载
) else (
    echo Node.js %1 未安装
)
goto :eof

:use_version
if "%1"=="" (
    echo 错误: 请指定要使用的Node.js版本
    goto :eof
)
if not exist "%NVM_HOME%\v%1" (
    echo 错误: Node.js %1 未安装
    goto :eof
)

REM 删除现有符号链接
if exist "%NVM_SYMLINK%" (
    rmdir "%NVM_SYMLINK%" 2>nul
)

REM 创建新的符号链接
mklink /D "%NVM_SYMLINK%" "%NVM_HOME%\v%1" >nul 2>&1
if errorlevel 1 (
    echo 错误: 无法创建符号链接，请以管理员身份运行
) else (
    echo 现在使用Node.js %1
)
goto :eof
"#, install_path);

        std::fs::write(&nvm_cmd, cmd_content)?;
        println!("  ✓ 创建nvm命令脚本: {}", nvm_cmd);

        // 创建PowerShell版本
        let nvm_ps1 = format!("{}/nvm.ps1", install_path);
        let ps1_content = format!(r#"# nvm for Windows PowerShell Script
param(
    [string]$Command,
    [string]$Version
)

$env:NVM_HOME = "{}"
$env:NVM_SYMLINK = "$env:ProgramFiles\nodejs"

switch ($Command) {{
    "version" {{
        Write-Host "1.1.11 for Windows (PowerShell)"
    }}
    "current" {{
        if (Test-Path "$env:NVM_SYMLINK\node.exe") {{
            try {{
                $version = & "$env:NVM_SYMLINK\node.exe" --version
                Write-Host $version
            }} catch {{
                Write-Host "未设置"
            }}
        }} else {{
            Write-Host "未设置"
        }}
    }}
    "list" {{
        if ($Version -eq "available") {{
            Write-Host "可用的Node.js版本 (部分):"
            Write-Host "  20.9.0 (LTS)"
            Write-Host "  18.18.2 (LTS)"
            Write-Host "  16.20.2 (LTS)"
            Write-Host "  21.2.0"
            Write-Host "  19.9.0"
            Write-Host "  14.21.3 (LTS)"
            Write-Host ""
            Write-Host "完整列表请访问: https://nodejs.org/en/download/releases/"
        }} else {{
            Write-Host "已安装的Node.js版本:"
            if (Test-Path $env:NVM_HOME) {{
                Get-ChildItem "$env:NVM_HOME\v*" -Directory | ForEach-Object {{
                    $versionName = $_.Name.Substring(1)
                    Write-Host "  $versionName"
                }}
            }} else {{
                Write-Host "  (无已安装版本)"
            }}
        }}
    }}
    "install" {{
        if (-not $Version) {{
            Write-Host "错误: 请指定要安装的Node.js版本" -ForegroundColor Red
            return
        }}
        Write-Host "正在安装Node.js $Version..." -ForegroundColor Green
        Write-Host "注意: 这是一个简化实现" -ForegroundColor Yellow
        Write-Host "请从 https://nodejs.org/en/download/ 手动下载Node.js $Version" -ForegroundColor Yellow
        Write-Host "并解压到 $env:NVM_HOME\v$Version\ 目录" -ForegroundColor Yellow
    }}
    "use" {{
        if (-not $Version) {{
            Write-Host "错误: 请指定要使用的Node.js版本" -ForegroundColor Red
            return
        }}

        $versionPath = "$env:NVM_HOME\v$Version"
        if (-not (Test-Path $versionPath)) {{
            Write-Host "错误: Node.js $Version 未安装" -ForegroundColor Red
            return
        }}

        # 删除现有符号链接
        if (Test-Path $env:NVM_SYMLINK) {{
            Remove-Item $env:NVM_SYMLINK -Force -Recurse -ErrorAction SilentlyContinue
        }}

        # 创建新的符号链接
        try {{
            New-Item -ItemType SymbolicLink -Path $env:NVM_SYMLINK -Target $versionPath -Force | Out-Null
            Write-Host "现在使用Node.js $Version" -ForegroundColor Green
        }} catch {{
            Write-Host "错误: 无法创建符号链接，请以管理员身份运行PowerShell" -ForegroundColor Red
        }}
    }}
    default {{
        Write-Host "nvm - Node.js版本管理器"
        Write-Host ""
        Write-Host "使用方法:"
        Write-Host "  nvm install [version]      - 安装Node.js版本"
        Write-Host "  nvm uninstall [version]    - 卸载Node.js版本"
        Write-Host "  nvm use [version]          - 使用指定版本"
        Write-Host "  nvm list                   - 列出已安装版本"
        Write-Host "  nvm list available         - 列出可用版本"
        Write-Host "  nvm current                - 显示当前版本"
        Write-Host "  nvm version                - 显示nvm版本"
    }}
}}
"#, install_path);

        std::fs::write(&nvm_ps1, ps1_content)?;
        println!("  ✓ 创建PowerShell脚本: {}", nvm_ps1);

        Ok(())
    }
    
    /// 配置环境变量
    async fn configure_environment(&self, install_path: &str) -> Result<()> {
        println!("🔧 配置nvm环境变量...");

        let symlink_path = format!("{}\\nodejs", std::env::var("ProgramFiles").unwrap_or_default());

        // 尝试自动配置环境变量
        match self.auto_configure_env_vars(install_path, &symlink_path).await {
            Ok(_) => {
                println!("  ✅ 环境变量配置成功");
                println!("  📝 已设置 NVM_HOME={}", install_path);
                println!("  📝 已设置 NVM_SYMLINK={}", symlink_path);
                println!("  📝 已添加到 PATH: {};{}", install_path, symlink_path);
            }
            Err(e) => {
                println!("  ⚠️ 自动配置失败: {}", e);
                println!("  📋 请手动配置以下环境变量:");
                println!("     NVM_HOME={}", install_path);
                println!("     NVM_SYMLINK={}", symlink_path);
                println!("     PATH=%PATH%;{};{}", install_path, symlink_path);
                println!("  💡 或者以管理员身份重新运行此程序");
            }
        }

        // 创建用户配置脚本
        self.create_user_config_scripts(install_path, &symlink_path).await?;

        Ok(())
    }

    /// 自动配置环境变量
    async fn auto_configure_env_vars(&self, install_path: &str, symlink_path: &str) -> Result<()> {
        use std::process::Command;

        // 设置NVM_HOME环境变量
        let setx_home = Command::new("setx")
            .args(&["NVM_HOME", install_path])
            .output()?;

        if !setx_home.status.success() {
            let error = String::from_utf8_lossy(&setx_home.stderr);
            return Err(anyhow::anyhow!("设置NVM_HOME失败: {}", error));
        }

        // 设置NVM_SYMLINK环境变量
        let setx_symlink = Command::new("setx")
            .args(&["NVM_SYMLINK", symlink_path])
            .output()?;

        if !setx_symlink.status.success() {
            let error = String::from_utf8_lossy(&setx_symlink.stderr);
            return Err(anyhow::anyhow!("设置NVM_SYMLINK失败: {}", error));
        }

        // 更新PATH环境变量
        let current_path = std::env::var("PATH").unwrap_or_default();
        let mut new_path = current_path;

        if !new_path.contains(install_path) {
            new_path = format!("{};{}", new_path, install_path);
        }
        if !new_path.contains(symlink_path) {
            new_path = format!("{};{}", new_path, symlink_path);
        }

        let setx_path = Command::new("setx")
            .args(&["PATH", &new_path])
            .output()?;

        if !setx_path.status.success() {
            let error = String::from_utf8_lossy(&setx_path.stderr);
            return Err(anyhow::anyhow!("更新PATH失败: {}", error));
        }

        Ok(())
    }

    /// 创建用户配置脚本
    async fn create_user_config_scripts(&self, install_path: &str, symlink_path: &str) -> Result<()> {
        println!("📝 创建用户配置脚本...");

        // 创建批处理配置脚本
        let setup_bat = format!("{}/setup.bat", install_path);
        let bat_content = format!(r#"@echo off
echo 配置nvm环境变量...

REM 设置环境变量
setx NVM_HOME "{}"
setx NVM_SYMLINK "{}"
setx PATH "%PATH%;{};{}"

echo.
echo ✅ nvm环境变量配置完成！
echo 请重新启动命令提示符或PowerShell以使更改生效。
echo.
echo 使用方法:
echo   nvm version        - 查看版本
echo   nvm list           - 查看已安装版本
echo   nvm install 18.18.0 - 安装Node.js 18.18.0
echo   nvm use 18.18.0    - 使用Node.js 18.18.0
echo.
pause
"#, install_path, symlink_path, install_path, symlink_path);

        std::fs::write(&setup_bat, bat_content)?;
        println!("  ✓ 创建批处理配置脚本: {}", setup_bat);

        // 创建PowerShell配置脚本
        let setup_ps1 = format!("{}/setup.ps1", install_path);
        let ps1_content = format!(r#"# nvm环境配置脚本
Write-Host "配置nvm环境变量..." -ForegroundColor Green

try {{
    # 设置环境变量
    [Environment]::SetEnvironmentVariable("NVM_HOME", "{}", "User")
    [Environment]::SetEnvironmentVariable("NVM_SYMLINK", "{}", "User")

    # 获取当前用户PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $nvmPath = "{}"
    $symlinkPath = "{}"

    $newPath = $currentPath
    if ($newPath -notlike "*$nvmPath*") {{
        $newPath = if ($newPath) {{ "$newPath;$nvmPath" }} else {{ $nvmPath }}
    }}
    if ($newPath -notlike "*$symlinkPath*") {{
        $newPath = "$newPath;$symlinkPath"
    }}

    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")

    Write-Host ""
    Write-Host "✅ nvm环境变量配置完成！" -ForegroundColor Green
    Write-Host "请重新启动PowerShell以使更改生效。" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "使用方法:"
    Write-Host "  nvm version        - 查看版本"
    Write-Host "  nvm list           - 查看已安装版本"
    Write-Host "  nvm install 18.18.0 - 安装Node.js 18.18.0"
    Write-Host "  nvm use 18.18.0    - 使用Node.js 18.18.0"

}} catch {{
    Write-Host "❌ 配置失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请以管理员身份运行此脚本" -ForegroundColor Yellow
}}

Read-Host "按任意键继续"
"#, install_path, symlink_path, install_path, symlink_path);

        std::fs::write(&setup_ps1, ps1_content)?;
        println!("  ✓ 创建PowerShell配置脚本: {}", setup_ps1);

        Ok(())
    }
    
    /// 检查是否为当前版本
    async fn is_current_version(&self, version: &str) -> Result<bool> {
        // 检查NVM_SYMLINK指向的版本
        if let Ok(nvm_symlink) = std::env::var("NVM_SYMLINK") {
            if let Ok(current_path) = std::fs::read_link(&nvm_symlink) {
                if let Some(current_dir) = current_path.file_name() {
                    let current_version = current_dir.to_string_lossy();
                    if current_version.starts_with('v') {
                        return Ok(&current_version[1..] == version);
                    }
                }
            }
        }
        
        // 尝试执行node命令获取当前版本
        let output = Command::new("node")
            .arg("--version")
            .output();
            
        match output {
            Ok(output) if output.status.success() => {
                let current_version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                if current_version.starts_with('v') {
                    Ok(&current_version[1..] == version)
                } else {
                    Ok(false)
                }
            }
            _ => Ok(false),
        }
    }
    
    /// 检查是否为LTS版本（简化判断）
    fn is_lts_version(&self, version: &str) -> bool {
        if let Some(major_version) = version.split('.').next() {
            if let Ok(major) = major_version.parse::<u32>() {
                // Node.js LTS版本通常是偶数主版本号
                return major % 2 == 0 && major >= 12;
            }
        }
        false
    }
}
