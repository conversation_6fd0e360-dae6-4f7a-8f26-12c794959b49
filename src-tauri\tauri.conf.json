{"$schema": "https://schema.tauri.app/config/2", "productName": "环境管理工具", "version": "1.0.0", "identifier": "com.envmanager.app", "build": {"beforeDevCommand": "", "devUrl": "http://localhost:1420", "beforeBuildCommand": "cd src-ui && npm run build", "frontendDist": "../src-ui/dist"}, "app": {"windows": [{"title": "环境管理工具", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "decorations": true}], "security": {"csp": null}}, "plugins": {}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}