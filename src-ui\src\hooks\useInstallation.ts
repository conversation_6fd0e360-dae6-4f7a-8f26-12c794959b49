import { useState, useCallback } from 'react';
import { useAppStore } from '../store';
import { TauriService, InstallConfig, handleTauriError } from '../services/tauri';

export const useInstallation = () => {
  const {
    isInstalling,
    installProgress,
    setInstalling,
    setInstallProgress,
    updateManager,
    setError,
    setNotification,
  } = useAppStore();

  const [installingManager, setInstallingManager] = useState<string | null>(null);
  const [uninstallingManager, setUninstallingManager] = useState<string | null>(null);

  // 安装管理器
  const installManager = useCallback(async (
    name: string,
    config: InstallConfig = {
      configure_env: true,
      options: {},
    }
  ) => {
    setInstallingManager(name);
    setInstalling(true);
    setInstallProgress(0);
    setError(null);

    try {
      // 更新管理器状态为安装中
      updateManager(name, { status: '安装中' });

      // 模拟安装进度
      const progressInterval = setInterval(() => {
        setInstallProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 500);

      // 执行安装
      await TauriService.installManager(name, config);

      // 清除进度定时器
      clearInterval(progressInterval);
      setInstallProgress(100);

      // 更新管理器状态
      updateManager(name, {
        status: '已安装',
        envConfigured: config.configure_env,
      });

      setNotification({
        type: 'success',
        message: '安装成功',
        description: `${name} 已成功安装`,
      });

      // 延迟重置进度
      setTimeout(() => {
        setInstallProgress(0);
      }, 1000);

    } catch (error) {
      const errorMessage = handleTauriError(error);

      // 更新管理器状态为安装失败
      updateManager(name, { status: '安装失败' });

      setError(errorMessage);
      setNotification({
        type: 'error',
        message: '安装失败',
        description: `${name} 安装失败: ${errorMessage}`,
      });

      setInstallProgress(0);
    } finally {
      setInstallingManager(null);
      setInstalling(false);
    }
  }, [
    setInstalling,
    setInstallProgress,
    updateManager,
    setError,
    setNotification,
  ]);

  // 卸载管理器
  const uninstallManager = useCallback(async (name: string) => {
    setUninstallingManager(name);
    setError(null);

    try {
      // 更新管理器状态为卸载中
      updateManager(name, { status: '卸载中' });

      // 执行卸载
      await TauriService.uninstallManager(name);

      // 更新管理器状态
      updateManager(name, {
        status: '未安装',
        version: undefined,
        installPath: undefined,
        envConfigured: false,
      });

      setNotification({
        type: 'success',
        message: '卸载成功',
        description: `${name} 已成功卸载`,
      });

    } catch (error) {
      const errorMessage = handleTauriError(error);

      // 恢复管理器状态
      updateManager(name, { status: '已安装' });

      setError(errorMessage);
      setNotification({
        type: 'error',
        message: '卸载失败',
        description: `${name} 卸载失败: ${errorMessage}`,
      });
    } finally {
      setUninstallingManager(null);
    }
  }, [updateManager, setError, setNotification]);

  // 检查特定管理器是否正在安装
  const isInstallingManager = useCallback((name: string) => {
    return installingManager === name;
  }, [installingManager]);

  // 检查特定管理器是否正在卸载
  const isUninstallingManager = useCallback((name: string) => {
    return uninstallingManager === name;
  }, [uninstallingManager]);

  // 获取安装进度百分比
  const getInstallProgressPercent = useCallback(() => {
    return Math.round(installProgress);
  }, [installProgress]);

  return {
    // 状态
    isInstalling,
    installProgress,
    installingManager,
    uninstallingManager,

    // 方法
    installManager,
    uninstallManager,
    isInstallingManager,
    isUninstallingManager,
    getInstallProgressPercent,
  };
};
