["\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\path\\autogenerated\\default.toml"]