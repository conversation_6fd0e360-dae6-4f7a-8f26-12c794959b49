import React, { useEffect } from 'react';
import { Button, Space } from 'antd';
import {
  MinusOutlined,
  BorderOutlined,
  CloseOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';

const CustomTitleBar: React.FC = () => {
  const handleMinimize = async () => {
    try {
      await invoke('minimize_window');
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  };

  const handleMaximize = async () => {
    try {
      await invoke('toggle_maximize');
    } catch (error) {
      console.error('Failed to maximize window:', error);
    }
  };

  const handleClose = async () => {
    try {
      await invoke('close_window');
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  };

  const handleRefresh = () => {
    // 直接使用浏览器刷新，更可靠
    window.location.reload();
  };

  // 添加F5键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F5') {
        event.preventDefault();
        // 直接调用刷新逻辑，避免依赖handleRefresh
        try {
          invoke('refresh_webview').catch(() => {
            window.location.reload();
          });
        } catch (error) {
          window.location.reload();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div
      data-tauri-drag-region
      style={{
        height: '32px',
        background: 'linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '0 16px',
        borderBottom: '1px solid #e1e4e8',
        userSelect: 'none',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}
    >
      {/* 应用标题 */}
      <div style={{ 
        fontSize: '14px', 
        fontWeight: 500, 
        color: '#1677FF',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <span>环境管理工具</span>
      </div>

      {/* 窗口控制按钮 */}
      <Space size={0}>
        <Button
          type="text"
          size="small"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          style={{
            width: '46px',
            height: '32px',
            border: 'none',
            borderRadius: 0,
            color: '#666',
            fontSize: '12px',
          }}
          className="title-bar-button"
          title="刷新页面 (F5)"
        />
        <Button
          type="text"
          size="small"
          icon={<MinusOutlined />}
          onClick={handleMinimize}
          style={{
            width: '46px',
            height: '32px',
            border: 'none',
            borderRadius: 0,
            color: '#666',
            fontSize: '12px',
          }}
          className="title-bar-button"
          title="最小化"
        />
        <Button
          type="text"
          size="small"
          icon={<BorderOutlined />}
          onClick={handleMaximize}
          style={{
            width: '46px',
            height: '32px',
            border: 'none',
            borderRadius: 0,
            color: '#666',
            fontSize: '12px',
          }}
          className="title-bar-button"
          title="最大化/还原"
        />
        <Button
          type="text"
          size="small"
          icon={<CloseOutlined />}
          onClick={handleClose}
          style={{
            width: '46px',
            height: '32px',
            border: 'none',
            borderRadius: 0,
            color: '#666',
            fontSize: '12px',
          }}
          className="title-bar-button close-button"
          title="关闭"
        />
      </Space>

      <style>{`
        .title-bar-button:hover {
          background-color: rgba(0, 0, 0, 0.1) !important;
        }

        .close-button:hover {
          background-color: #e81123 !important;
          color: white !important;
        }

        .title-bar-button:active {
          background-color: rgba(0, 0, 0, 0.2) !important;
        }

        .close-button:active {
          background-color: #c50e1f !important;
        }
      `}</style>
    </div>
  );
};

export default CustomTitleBar;
