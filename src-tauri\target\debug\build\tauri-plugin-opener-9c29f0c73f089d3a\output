cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-opener-9c29f0c73f089d3a\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-opener-9c29f0c73f089d3a\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\environment\rust\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-opener-2.5.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
