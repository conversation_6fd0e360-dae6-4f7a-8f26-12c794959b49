{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15020664462999436276, "build_script_build", false, 9720691151275921530], [16429266147849286097, "build_script_build", false, 1128930351115728868]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-opener-9c29f0c73f089d3a\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}