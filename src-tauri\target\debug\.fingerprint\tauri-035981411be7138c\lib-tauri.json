{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 1037318687954821583, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3876097705697069673], [1260461579271933187, "serialize_to_javascript", false, 15242896228147194212], [1967864351173319501, "muda", false, 18282658162225912458], [2013030631243296465, "webview2_com", false, 9027294774060575009], [3331586631144870129, "getrandom", false, 5929221717173722416], [3899750328741010762, "tauri_runtime_wry", false, 6096128943482174274], [4143744114649553716, "raw_window_handle", false, 17752533427338865920], [4352886507220678900, "serde_json", false, 1059408172161946174], [4537297827336760846, "thiserror", false, 9766178559347330724], [5986029879202738730, "log", false, 7881454566519146328], [6537120525306722933, "tauri_macros", false, 57151010819681008], [6803352382179706244, "percent_encoding", false, 15812198593072166791], [9010263965687315507, "http", false, 13052604301562925712], [9293239362693504808, "glob", false, 8192240580272692627], [9689903380558560274, "serde", false, 10535885796594175147], [10229185211513642314, "mime", false, 7829287207143260174], [11207653606310558077, "anyhow", false, 8094606839429439598], [11989259058781683633, "dunce", false, 12204059630274962688], [12565293087094287914, "window_vibrancy", false, 16271584728369884341], [12986574360607194341, "serde_repr", false, 6403850145008945355], [13076268252722892340, "url", false, 8042336736044679275], [13077543566650298139, "heck", false, 6637553969213554552], [14585479307175734061, "windows", false, 15438721831857517499], [15020664462999436276, "build_script_build", false, 9720691151275921530], [16727543399706004146, "cookie", false, 18099651654244230613], [16928111194414003569, "dirs", false, 13722319158371109550], [17233053221795943287, "tauri_utils", false, 14638709583540406129], [17531218394775549125, "tokio", false, 17060898677026264165], [18010483002580779355, "tauri_runtime", false, 16336778403848337715]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-035981411be7138c\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}