{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 11933756421952101080, "profile": 8731458305071235362, "path": 5265518918338594008, "deps": [[1630737303963722877, "tauri_plugin_dialog", false, 3381454567523555777], [3533833876439050747, "version_compare", false, 7508540095718168941], [3722963349756955755, "once_cell", false, 13611058840668907525], [4352886507220678900, "serde_json", false, 12094211512695454226], [4707735785701411121, "tauri_plugin_window_state", false, 11970740328964555111], [4716821492401257289, "zip", false, 4970790990031165733], [6079318424673677659, "sha256", false, 14967175204019079819], [7244058819997729774, "reqwest", false, 15185147357315279197], [8008191657135824715, "thiserror", false, 222108149844089593], [9451456094439810778, "regex", false, 15058832543788581855], [9689903380558560274, "serde", false, 11451862114754676285], [9857275760291862238, "sha2", false, 6439689227161735315], [9897246384292347999, "chrono", false, 4238719477588613375], [10629569228670356391, "futures_util", false, 17894635917108000607], [11207653606310558077, "anyhow", false, 728085163270764637], [12400935292645517134, "build_script_build", false, 6290034441356952113], [14285978758320820277, "tauri_plugin_fs", false, 117528126388195935], [15020664462999436276, "tauri", false, 2557015631512589513], [15609422047640926750, "toml", false, 5790834706111674765], [16429266147849286097, "tauri_plugin_opener", false, 4799087931519355067], [16611674984963787466, "async_trait", false, 18398624175857034335], [16928111194414003569, "dirs", false, 10321179744037770892], [17121285809968213651, "md5", false, 5715575988746372900], [17531218394775549125, "tokio", false, 8368688754076438113], [17772299992546037086, "flate2", false, 8744069167109400916]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\env-manager-87727ba1ebe86529\\dep-lib-env_manager_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}