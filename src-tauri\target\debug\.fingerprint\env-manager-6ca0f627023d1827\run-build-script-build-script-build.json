{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12400935292645517134, "build_script_build", false, 14333841755847215996], [15020664462999436276, "build_script_build", false, 13554751904377777979], [1630737303963722877, "build_script_build", false, 15396425589988610599], [14285978758320820277, "build_script_build", false, 2112238773315045480], [16429266147849286097, "build_script_build", false, 630220568966877063], [4707735785701411121, "build_script_build", false, 5215471053685159658]], "local": [{"RerunIfChanged": {"output": "debug\\build\\env-manager-6ca0f627023d1827\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}