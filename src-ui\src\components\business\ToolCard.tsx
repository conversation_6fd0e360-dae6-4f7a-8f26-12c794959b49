import React from 'react';
import { Card, Button, Tag, Space, Typography, Divider, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  DownloadOutlined,
  SettingOutlined,
  CloseCircleOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import { useDetection } from '../../hooks/useDetection';
import { useInstallation } from '../../hooks/useInstallation';

const { Text, Paragraph } = Typography;

interface ManagerInfo {
  name: string;
  description: string;
  status: string;
  version?: string;
  installPath?: string;
  envConfigured?: boolean;
  metadata?: Record<string, string>;
}

interface ToolCardProps {
  manager: ManagerInfo;
  onDetect: () => void;
  onInstall: () => void;
  onConfigure: () => void;
  onManageVersions: () => void;
}

const ToolCard: React.FC<ToolCardProps> = ({
  manager,
  onDetect,
  onInstall,
  onConfigure,
  onManageVersions,
}) => {
  const { isDetectingManager } = useDetection();
  const { isInstallingManager } = useInstallation();

  // 获取简短描述（限制在2行内显示）
  const getShortDescription = (description: string): string => {
    // 首先尝试按句号分割
    const firstSentence = description.split('。')[0];

    // 如果第一句话不超过30个字符，使用第一句话
    if (firstSentence.length <= 30) {
      return firstSentence + (description.includes('。') ? '。' : '');
    }

    // 否则截取前30个字符
    if (description.length <= 30) {
      return description;
    }

    return description.substring(0, 27) + '...';
  };
  const getStatusConfig = (status: string) => {
    switch (status) {
      case '已安装':
        return {
          color: 'success',
          icon: <CheckCircleOutlined />,
          text: '已安装',
        };
      case '未安装':
        return {
          color: 'warning',
          icon: <ExclamationCircleOutlined />,
          text: '未安装',
        };
      case '安装中':
        return {
          color: 'processing',
          icon: <SyncOutlined spin />,
          text: '安装中',
        };
      case '错误':
        return {
          color: 'error',
          icon: <CloseCircleOutlined />,
          text: '错误',
        };
      default:
        return {
          color: 'default',
          icon: <ExclamationCircleOutlined />,
          text: '未检测',
        };
    }
  };

  const statusConfig = getStatusConfig(manager.status);
  const isInstalled = manager.status === '已安装';
  const isInstalling = manager.status === '安装中' || isInstallingManager(manager.name);
  const isDetecting = isDetectingManager(manager.name);

  return (
    <Card
      hoverable
      style={{
        borderRadius: 8,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        height: '300px', // 增加卡片高度以容纳内容
        display: 'flex',
        flexDirection: 'column',
      }}
      styles={{
        body: {
          padding: '20px',
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          marginBottom: 8 
        }}>
          <Text strong style={{ fontSize: 16 }}>
            {manager.name}
          </Text>
          <Tag 
            color={statusConfig.color} 
            icon={statusConfig.icon}
          >
            {statusConfig.text}
          </Tag>
        </div>
        
        <Tooltip title={manager.description} placement="bottom">
          <Paragraph
            type="secondary"
            style={{
              margin: 0,
              fontSize: 14,
              lineHeight: 1.4,
              cursor: 'help',
              height: '40px', // 固定高度，约2行文字
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {getShortDescription(manager.description)}
          </Paragraph>
        </Tooltip>
        
        {manager.version && (
          <Text type="secondary" style={{ fontSize: 12 }}>
            版本: {manager.version}
          </Text>
        )}
      </div>

      <Divider style={{ margin: '16px 0' }} />

      <div style={{ flex: 1 }} />

      <Space direction="vertical" style={{ width: '100%' }}>
        <Button
          block
          icon={<SyncOutlined spin={isDetecting} />}
          onClick={onDetect}
          disabled={isInstalling || isDetecting}
          loading={isDetecting}
        >
          {isDetecting ? '检测中...' : '检测状态'}
        </Button>
        
        {!isInstalled && (
          <Button
            block
            type="primary"
            icon={<DownloadOutlined />}
            onClick={onInstall}
            loading={isInstalling}
            disabled={isInstalling}
          >
            {isInstalling ? '安装中...' : '安装'}
          </Button>
        )}
        
        {isInstalled && (
          <>
            <Button
              block
              type="primary"
              icon={<AppstoreOutlined />}
              onClick={onManageVersions}
              style={{ marginBottom: 8 }}
            >
              管理版本
            </Button>
            <Button
              block
              icon={<SettingOutlined />}
              onClick={onConfigure}
            >
              配置
            </Button>
          </>
        )}
      </Space>
    </Card>
  );
};

export default ToolCard;
