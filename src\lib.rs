// Windows环境管理工具

// 模块声明
pub mod commands;
pub mod models;
pub mod utils;
pub mod config;
pub mod traits;
// pub mod core;

// 管理器模块 - 暂时注释掉以解决编译问题
// pub mod managers;

// 重新导出主要类型 - 使用具体导出避免命名冲突
pub use models::{ManagerInfo, VersionInfo, InstallConfig, SystemInfo};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志系统
    if let Err(e) = utils::logger::init_logger() {
        eprintln!("Failed to initialize logger: {}", e);
    }

    log_info!("APP", "环境管理器启动");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .invoke_handler(tauri::generate_handler![
            // 检测相关命令
            commands::detection::get_managers,
            commands::detection::detect_all_managers,
            commands::detection::detect_manager,
            commands::detection::get_supported_managers,
            commands::detection::validate_manager,

            // 安装相关命令
            commands::installation::install_manager,
            commands::installation::uninstall_manager,

            // 配置相关命令
            commands::configuration::configure_manager,
            commands::configuration::get_app_config,
            commands::configuration::update_app_config,
            commands::configuration::save_config,

            // 系统相关命令
            commands::system::get_system_info,

            // 版本管理相关命令
            commands::detection::get_installed_versions,
            commands::detection::get_available_versions,
            commands::installation::install_version,
            commands::installation::uninstall_version,
            commands::configuration::set_current_version,
            commands::configuration::get_current_version,

            // 包管理相关命令
            commands::packages::get_installed_packages,
            commands::packages::get_available_packages,
            commands::packages::install_package,
            commands::packages::uninstall_package,

            // 版本管理相关命令
            commands::version::set_default_version,
            commands::version::get_app_version,
            commands::version::verify_environment,
            commands::version::refresh_current_version,

            // 日志管理相关命令
            commands::logs::get_logs,
            commands::logs::clear_logs,
            commands::logs::export_logs,
            commands::logs::get_log_stats,
            commands::logs::set_log_config,
            commands::logs::rotate_logs,
            commands::logs::get_log_file_stats,
            commands::logs::cleanup_logs,
            commands::logs::set_log_file_size_limit,
            commands::logs::set_log_file_count_limit,

            // 窗口控制相关命令
            commands::window::minimize_window,
            commands::window::toggle_maximize,
            commands::window::close_window,
            commands::window::refresh_webview,
            commands::window::get_window_state,
            commands::window::set_window_title,
            commands::window::execute_nvm_command_safe,

            // Windows集成相关命令 - 暂时注释掉以解决编译问题
            // commands::windows_integration::get_windows_managers_info,

            // 应用配置相关命令 - 暂时注释掉以避免重复定义
            // commands::app_config::get_app_config,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
