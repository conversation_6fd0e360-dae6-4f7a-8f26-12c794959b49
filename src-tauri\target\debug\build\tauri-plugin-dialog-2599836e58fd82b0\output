cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-dialog-2599836e58fd82b0\out\tauri-plugin-dialog-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\environment\rust\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-dialog-2.3.3\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
