import { invoke } from '@tauri-apps/api/core';
import { ManagerInfo, AppConfig } from '../store';

// 检查Tauri API是否可用
const isTauriAvailable = () => {
  try {
    // 检查多种可能的Tauri API标识
    const hasWindow = typeof window !== 'undefined';
    const hasTauri = hasWindow && (
      (window as any).__TAURI__ ||
      (window as any).__TAURI_INTERNALS__ ||
      (window as any).__TAURI_METADATA__
    );

    console.log('Tauri可用性检查:', {
      hasWindow,
      hasTauri,
      windowTauri: hasWindow ? !!(window as any).__TAURI__ : false,
      windowTauriInternals: hasWindow ? !!(window as any).__TAURI_INTERNALS__ : false,
      windowTauriMetadata: hasWindow ? !!(window as any).__TAURI_METADATA__ : false,
    });

    return hasTauri;
  } catch (error) {
    console.error('检查Tauri API可用性时出错:', error);
    return false;
  }
};

// 安装配置接口
export interface InstallConfig {
  install_path?: string;
  version?: string;
  configure_env: boolean;
  options: Record<string, string>;
}

// 设置接口
export interface Settings {
  config: Record<string, string>;
}

// 系统信息接口
export interface SystemInfo {
  os: string;
  arch: string;
  version: string;
  availableSpace: number;
  totalMemory: number;
}

// 版本信息接口
export interface VersionInfo {
  version: string;
  status: 'installed' | 'available' | 'installing' | 'current';
  description?: string;
  releaseDate?: string;
  isLTS?: boolean;
}

// Tauri 命令封装类
export class TauriService {
  // 检测相关命令
  static async getManagers(): Promise<ManagerInfo[]> {
    try {
      return await invoke<ManagerInfo[]>('get_managers');
    } catch (error) {
      console.error('获取管理器列表失败:', error);
      throw new Error(`获取管理器列表失败: ${error}`);
    }
  }

  static async detectAllManagers(): Promise<ManagerInfo[]> {
    try {
      const tauriAvailable = isTauriAvailable();
      console.log('TauriService.detectAllManagers: Tauri可用性检查:', tauriAvailable);

      console.log('TauriService.detectAllManagers: 尝试调用真实的detect_all_managers命令');
      const result = await invoke<ManagerInfo[]>('detect_all_managers');
      console.log('TauriService.detectAllManagers: 检测结果:', result);
      return result;
    } catch (error) {
      console.error('TauriService.detectAllManagers: invoke调用失败:', error);
      console.log('TauriService.detectAllManagers: 回退到模拟数据');

      // 如果invoke失败，回退到模拟数据
      return [
        {
          name: 'SDKMan',
          description: 'Java开发工具包管理器',
          status: '未安装',
          version: undefined,
          installPath: undefined,
          envConfigured: false,
          metadata: {
            supportedLanguages: 'java,kotlin,scala',
            website: 'https://sdkman.io/'
          }
        },
        {
          name: 'pyenv',
          description: 'Python版本管理器',
          status: '已安装',
          version: '2.3.0',
          installPath: 'D:\\environment\\pyenv\\pyenv-win',
          envConfigured: true,
          metadata: {
            supportedLanguages: 'python',
            website: 'https://github.com/pyenv/pyenv'
          }
        },
        {
          name: 'nvm',
          description: 'Node.js版本管理器',
          status: '已安装',
          version: '1.1.7',
          installPath: 'D:\\environment\\Nvm\\nvm',
          envConfigured: true,
          metadata: {
            supportedLanguages: 'nodejs',
            website: 'https://github.com/coreybutler/nvm-windows'
          }
        }
      ];
    }
  }

  static async detectManager(name: string): Promise<ManagerInfo> {
    try {
      return await invoke<ManagerInfo>('detect_manager', { name });
    } catch (error) {
      console.error(`检测管理器 ${name} 失败:`, error);
      throw new Error(`检测管理器 ${name} 失败: ${error}`);
    }
  }

  static async getSupportedManagers(): Promise<string[]> {
    try {
      return await invoke<string[]>('get_supported_managers');
    } catch (error) {
      console.error('获取支持的管理器失败:', error);
      throw new Error(`获取支持的管理器失败: ${error}`);
    }
  }

  static async validateManager(name: string): Promise<boolean> {
    try {
      return await invoke<boolean>('validate_manager', { name });
    } catch (error) {
      console.error(`验证管理器 ${name} 失败:`, error);
      throw new Error(`验证管理器 ${name} 失败: ${error}`);
    }
  }

  // 安装相关命令
  static async installManager(name: string, config: InstallConfig): Promise<void> {
    try {
      await invoke<void>('install_manager', { name, config });
    } catch (error) {
      console.error(`安装管理器 ${name} 失败:`, error);
      throw new Error(`安装管理器 ${name} 失败: ${error}`);
    }
  }

  static async uninstallManager(name: string): Promise<void> {
    try {
      await invoke<void>('uninstall_manager', { name });
    } catch (error) {
      console.error(`卸载管理器 ${name} 失败:`, error);
      throw new Error(`卸载管理器 ${name} 失败: ${error}`);
    }
  }

  // 配置相关命令
  static async configureManager(name: string, settings: Settings): Promise<void> {
    try {
      await invoke<void>('configure_manager', { name, settings });
    } catch (error) {
      console.error(`配置管理器 ${name} 失败:`, error);
      throw new Error(`配置管理器 ${name} 失败: ${error}`);
    }
  }

  static async getAppConfig(): Promise<AppConfig> {
    try {
      return await invoke<AppConfig>('get_app_config');
    } catch (error) {
      console.error('获取应用配置失败:', error);
      throw new Error(`获取应用配置失败: ${error}`);
    }
  }

  static async updateAppConfig(config: AppConfig): Promise<void> {
    try {
      await invoke<void>('update_app_config', { config });
    } catch (error) {
      console.error('更新应用配置失败:', error);
      throw new Error(`更新应用配置失败: ${error}`);
    }
  }

  static async saveConfig(): Promise<void> {
    try {
      await invoke<void>('save_config');
    } catch (error) {
      console.error('保存配置失败:', error);
      throw new Error(`保存配置失败: ${error}`);
    }
  }

  // 系统相关命令
  static async getSystemInfo(): Promise<SystemInfo> {
    try {
      return await invoke<SystemInfo>('get_system_info');
    } catch (error) {
      console.error('获取系统信息失败:', error);
      throw new Error(`获取系统信息失败: ${error}`);
    }
  }

  // 版本管理相关命令
  static async getInstalledVersions(managerName: string): Promise<VersionInfo[]> {
    try {
      return await invoke<VersionInfo[]>('get_installed_versions', { managerName });
    } catch (error) {
      console.error(`获取${managerName}已安装版本失败:`, error);
      throw new Error(`获取${managerName}已安装版本失败: ${error}`);
    }
  }

  static async getAvailableVersions(managerName: string): Promise<VersionInfo[]> {
    try {
      return await invoke<VersionInfo[]>('get_available_versions', { managerName });
    } catch (error) {
      console.error(`获取${managerName}可用版本失败:`, error);
      throw new Error(`获取${managerName}可用版本失败: ${error}`);
    }
  }

  static async installVersion(managerName: string, version: string): Promise<void> {
    try {
      await invoke<void>('install_version', { managerName, version });
    } catch (error) {
      console.error(`安装${managerName} ${version}失败:`, error);
      throw new Error(`安装${managerName} ${version}失败: ${error}`);
    }
  }

  static async uninstallVersion(managerName: string, version: string): Promise<void> {
    try {
      await invoke<void>('uninstall_version', { managerName, version });
    } catch (error) {
      console.error(`卸载${managerName} ${version}失败:`, error);
      throw new Error(`卸载${managerName} ${version}失败: ${error}`);
    }
  }

  static async setCurrentVersion(managerName: string, version: string): Promise<void> {
    try {
      await invoke<void>('set_current_version', { managerName, version });
    } catch (error) {
      console.error(`设置${managerName}当前版本为${version}失败:`, error);
      throw new Error(`设置${managerName}当前版本为${version}失败: ${error}`);
    }
  }

  static async getCurrentVersion(managerName: string): Promise<string | null> {
    try {
      return await invoke<string | null>('get_current_version', { managerName });
    } catch (error) {
      console.error(`获取${managerName}当前版本失败:`, error);
      throw new Error(`获取${managerName}当前版本失败: ${error}`);
    }
  }
}

// 错误处理工具函数
export const handleTauriError = (error: unknown): string => {
  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return '未知错误';
};

// 重试机制
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: unknown;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};
