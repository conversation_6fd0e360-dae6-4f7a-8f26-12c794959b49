{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12400935292645517134, "build_script_build", false, 10663819880445571542], [15020664462999436276, "build_script_build", false, 11936638175539523668], [1630737303963722877, "build_script_build", false, 17770599213057896084], [14285978758320820277, "build_script_build", false, 973345240078540455], [16429266147849286097, "build_script_build", false, 6325742514194816569], [4707735785701411121, "build_script_build", false, 6417565519577476925]], "local": [{"RerunIfChanged": {"output": "debug\\build\\env-manager-025a83bbe735ce68\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}