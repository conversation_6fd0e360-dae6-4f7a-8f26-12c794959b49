import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Button, Space, Typography, Alert } from 'antd';
import {
  ToolOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import ToolCard from '../components/business/ToolCard';
import Loading from '../components/common/Loading';
import InstallationProgress from '../components/business/InstallationProgress';
import ConfigPanel from '../components/business/ConfigPanel';
// import SystemInfo from '../components/business/SystemInfo'; // 暂时未使用
import VersionManager from '../components/business/VersionManager';
import { useDetection } from '../hooks/useDetection';
import { useInstallation } from '../hooks/useInstallation';
import { useAppStore } from '../store';

const { Title, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const { managers, isDetecting, detectAllManagers, detectManager } = useDetection();
  const {
    installManager,
    isInstalling,
    installingManager,
    getInstallProgressPercent
  } = useInstallation();
  const { error } = useAppStore();

  // 配置面板状态
  const [configVisible, setConfigVisible] = React.useState(false);
  const [configManager, setConfigManager] = React.useState<string>('');

  // 版本管理状态
  const [versionManagerVisible, setVersionManagerVisible] = React.useState(false);
  const [versionManagerName, setVersionManagerName] = React.useState<string>('');

  useEffect(() => {
    // 初始加载管理器数据
    console.log('Dashboard: 开始检测所有管理器');
    console.info('Dashboard: 应用启动时间:', new Date().toISOString());
    detectAllManagers();

    // 添加一些测试日志
    console.debug('Dashboard: 调试信息 - 组件已挂载');
    console.warn('Dashboard: 这是一个警告示例');

    // 模拟一个错误（仅用于测试）
    setTimeout(() => {
      try {
        // 这会产生一个错误用于测试
        // throw new Error('这是一个测试错误，用于演示开发者控制台');
      } catch (error) {
        console.error('Dashboard: 捕获到错误:', error);
      }
    }, 2000);

    // 添加键盘快捷键支持 (F5 刷新)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F5' && !isDetecting) {
        event.preventDefault();
        console.log('Dashboard: F5 快捷键触发刷新');
        detectAllManagers();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []); // 移除依赖项避免无限循环

  const installedCount = managers.filter(m => m.status === '已安装').length;
  const totalCount = managers.length;

  const handleInstall = async (managerName: string) => {
    await installManager(managerName, {
      configure_env: true,
      options: {},
    });
  };

  const handleConfigure = (managerName: string) => {
    setConfigManager(managerName);
    setConfigVisible(true);
  };

  const handleManageVersions = (managerName: string) => {
    setVersionManagerName(managerName);
    setVersionManagerVisible(true);
  };

  const handleConfigSave = async (config: Record<string, any>) => {
    try {
      // TODO: 调用Tauri命令保存配置
      console.log('保存配置:', configManager, config);
      setConfigVisible(false);

      // 显示成功通知
      useAppStore.getState().setNotification({
        type: 'success',
        message: '配置保存成功',
        description: `${configManager} 的配置已更新`,
      });
    } catch (error) {
      console.error('保存配置失败:', error);
      useAppStore.getState().setNotification({
        type: 'error',
        message: '配置保存失败',
        description: String(error),
      });
    }
  };

  if (isDetecting && managers.length === 0) {
    return <Loading tip="正在检测系统中的工具..." size="large" />;
  }

  return (
    <div style={{
      height: '100%',
      overflow: 'auto',
      padding: '0 8px' // 减少左右padding，为滚动条留出空间
    }}>

      {error && (
        <Alert
          message="操作失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => {
            const { setError } = useAppStore.getState();
            setError(null);
          }}
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="支持的工具"
              value={totalCount}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#1677FF' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="已安装"
              value={installedCount}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52C41A' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="未安装"
              value={totalCount - installedCount}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#FAAD14' }}
            />
          </Card>
        </Col>
      </Row>

      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 16 
      }}>
        <Title level={3} style={{ margin: 0 }}>工具状态</Title>
        <Space>
          <Button
            type="primary"
            icon={<SyncOutlined spin={isDetecting} />}
            onClick={detectAllManagers}
            loading={isDetecting}
            disabled={isDetecting}
            title="刷新工具状态 (F5)"
          >
            {isDetecting ? '检测中...' : '刷新状态'}
          </Button>
          {!isDetecting && (
            <span style={{ fontSize: '12px', color: '#666' }}>
              提示: 按 F5 快速刷新
            </span>
          )}
        </Space>
      </div>

      <Row gutter={[16, 16]}>
        {managers.map((manager) => (
          <Col xs={24} sm={12} lg={8} key={manager.name}>
            <ToolCard
              manager={manager}
              onDetect={() => detectManager(manager.name)}
              onInstall={() => handleInstall(manager.name)}
              onConfigure={() => handleConfigure(manager.name)}
              onManageVersions={() => handleManageVersions(manager.name)}
            />
          </Col>
        ))}
      </Row>

      {managers.length === 0 && !isDetecting && (
        <Card style={{ textAlign: 'center', padding: '48px 24px' }}>
          <ToolOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">暂无工具数据</Title>
          <Paragraph type="secondary">
            点击刷新按钮重新加载工具信息
          </Paragraph>
          <Button type="primary" onClick={detectAllManagers}>
            重新加载
          </Button>
        </Card>
      )}

      {/* 安装进度模态框 */}
      <InstallationProgress
        visible={isInstalling}
        managerName={installingManager || ''}
        progress={getInstallProgressPercent()}
        status="installing"
        onCancel={() => {
          // TODO: 实现取消安装逻辑
          console.log('取消安装');
        }}
      />

      {/* 配置面板 */}
      <ConfigPanel
        visible={configVisible}
        managerName={configManager}
        onSave={handleConfigSave}
        onCancel={() => setConfigVisible(false)}
      />

      {/* 版本管理器 */}
      <VersionManager
        visible={versionManagerVisible}
        managerName={versionManagerName}
        onClose={() => setVersionManagerVisible(false)}
      />
    </div>
  );
};

export default Dashboard;
