cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-fs-cbfe527109e9ea7b\out\tauri-plugin-fs-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-fs-cbfe527109e9ea7b\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\environment\rust\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-fs-2.4.2\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
