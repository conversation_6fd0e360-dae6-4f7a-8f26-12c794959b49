{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 17935818335149578806, "profile": 15657897354478470176, "path": 2698676330948592420, "deps": [[4352886507220678900, "serde_json", false, 12094211512695454226], [4537297827336760846, "thiserror", false, 11802890791083710778], [4707735785701411121, "build_script_build", false, 5215471053685159658], [5986029879202738730, "log", false, 12586403485327171030], [9689903380558560274, "serde", false, 11451862114754676285], [9804395174584905611, "bitflags", false, 10349697702991389374], [15020664462999436276, "tauri", false, 2557015631512589513]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-window-state-220663176799bf75\\dep-lib-tauri_plugin_window_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}