cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=G:\code\my-tool\env-manager\src-tauri\target\debug\build\tauri-plugin-window-state-5aec5d0e93b2ccdb\out\tauri-plugin-window-state-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\environment\rust\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-window-state-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
