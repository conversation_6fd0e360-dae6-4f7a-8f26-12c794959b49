use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Window};
use crate::utils::safe_command::{execute_command_safe, SafeCommandConfig};

/// 最小化窗口
#[tauri::command]
pub async fn minimize_window(window: Window) -> Result<(), String> {
    window.minimize().map_err(|e| e.to_string())
}

/// 切换最大化状态
#[tauri::command]
pub async fn toggle_maximize(window: Window) -> Result<(), String> {
    let is_maximized = window.is_maximized().map_err(|e| e.to_string())?;
    
    if is_maximized {
        window.unmaximize().map_err(|e| e.to_string())
    } else {
        window.maximize().map_err(|e| e.to_string())
    }
}

/// 关闭窗口
#[tauri::command]
pub async fn close_window(window: Window) -> Result<(), String> {
    window.close().map_err(|e| e.to_string())
}

/// 刷新webview
#[tauri::command]
pub async fn refresh_webview(app_handle: AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        // 重新加载webview内容
        window.eval("window.location.reload()").map_err(|e| e.to_string())?;
        Ok(())
    } else {
        Err("Main window not found".to_string())
    }
}

/// 获取窗口状态
#[tauri::command]
pub async fn get_window_state(window: Window) -> Result<WindowState, String> {
    let is_maximized = window.is_maximized().map_err(|e| e.to_string())?;
    let is_minimized = window.is_minimized().map_err(|e| e.to_string())?;
    let is_focused = window.is_focused().map_err(|e| e.to_string())?;
    
    Ok(WindowState {
        is_maximized,
        is_minimized,
        is_focused,
    })
}

/// 设置窗口标题
#[tauri::command]
pub async fn set_window_title(window: Window, title: String) -> Result<(), String> {
    window.set_title(&title).map_err(|e| e.to_string())
}

/// 安全执行NVM命令
#[tauri::command]
pub async fn execute_nvm_command_safe(
    command: String,
    args: Vec<String>,
    allow_popup: bool
) -> Result<String, String> {
    let config = SafeCommandConfig {
        allow_popup,
        timeout_seconds: 30,
        background: true,
        working_dir: None,
    };

    let args_str: Vec<&str> = args.iter().map(|s| s.as_str()).collect();
    execute_command_safe(&command, &args_str, config)
        .map_err(|e| e.to_string())
}

#[derive(serde::Serialize)]
pub struct WindowState {
    pub is_maximized: bool,
    pub is_minimized: bool,
    pub is_focused: bool,
}
