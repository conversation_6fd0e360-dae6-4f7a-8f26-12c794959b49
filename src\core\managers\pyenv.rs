use std::process::Command;
use anyhow::Result;
use async_trait::async_trait;

use crate::core::engine::{PackageManager, Settings};
use crate::models::{ManagerInfo, VersionInfo, InstallConfig};
use crate::utils::download::download_file;

/// pyenv包管理器实现
pub struct PyenvManager {
    name: String,
    description: String,
}

impl PyenvManager {
    pub fn new() -> Self {
        Self {
            name: "pyenv".to_string(),
            description: "Python版本管理工具，可以轻松切换Python版本，支持多版本并存".to_string(),
        }
    }
}

#[async_trait]
impl PackageManager for PyenvManager {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
    
    async fn detect_installation(&self) -> Result<ManagerInfo> {
        let mut manager = ManagerInfo::new(self.name.clone(), self.description.clone());
        
        // 检查pyenv-win环境变量
        if let Ok(pyenv_root) = std::env::var("PYENV").or_else(|_| std::env::var("PYENV_ROOT")) {
            let pyenv_path = std::path::Path::new(&pyenv_root);
            if pyenv_path.exists() {
                // 尝试获取版本信息
                if let Ok(version) = self.get_version().await {
                    manager = manager
                        .with_status("已安装".to_string())
                        .with_version(version)
                        .with_install_path(pyenv_root)
                        .with_env_configured(true);
                } else {
                    manager = manager
                        .with_status("已安装但配置异常".to_string())
                        .with_install_path(pyenv_root)
                        .with_env_configured(false);
                }
            } else {
                manager = manager.with_status("环境变量存在但路径无效".to_string());
            }
        } else {
            // 检查默认安装路径
            let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
            let default_path = format!("{}\\.pyenv", home_dir);
            if std::path::Path::new(&default_path).exists() {
                manager = manager
                    .with_status("已安装但未配置环境变量".to_string())
                    .with_install_path(default_path)
                    .with_env_configured(false);
            } else {
                manager = manager.with_status("未安装".to_string());
            }
        }
        
        Ok(manager)
    }
    
    async fn install(&self, config: InstallConfig) -> Result<()> {
        // 获取安装路径
        let install_path = config.install_path.unwrap_or_else(|| {
            let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
            format!("{}\\.pyenv", home_dir)
        });

        println!("开始安装pyenv-win到: {}", install_path);

        // 创建目录结构
        self.create_directory_structure(&install_path).await?;

        // 下载pyenv-win
        self.download_pyenv_win(&install_path).await?;

        // 创建Windows脚本
        self.create_windows_scripts(&install_path).await?;

        // 配置环境变量
        if config.auto_configure_env.unwrap_or(true) {
            self.configure_environment(&install_path).await?;
        }

        // 验证安装
        if self.validate_installation().await? {
            println!("✅ pyenv-win安装成功！");
        } else {
            return Err(anyhow::anyhow!("❌ pyenv-win安装验证失败"));
        }

        Ok(())
    }
    
    async fn uninstall(&self) -> Result<()> {
        if let Ok(pyenv_root) = std::env::var("PYENV").or_else(|_| std::env::var("PYENV_ROOT")) {
            // 删除安装目录
            std::fs::remove_dir_all(&pyenv_root)?;
            
            // 清理环境变量
            println!("请手动从系统环境变量中删除 PYENV、PYENV_ROOT 和相关的PATH配置");
            
            Ok(())
        } else {
            Err(anyhow::anyhow!("pyenv未安装或环境变量未配置"))
        }
    }
    
    async fn get_version(&self) -> Result<String> {
        let output = Command::new("pyenv")
            .arg("--version")
            .output()?;
            
        if output.status.success() {
            let version_output = String::from_utf8_lossy(&output.stdout);
            let trimmed = version_output.trim();
            if let Some(version_part) = trimmed.split_whitespace().nth(1) {
                Ok(version_part.to_string())
            } else {
                Ok("unknown".to_string())
            }
        } else {
            Err(anyhow::anyhow!("无法获取pyenv版本信息"))
        }
    }
    
    async fn configure(&self, settings: Settings) -> Result<()> {
        if let Some(install_path) = &settings.install_path {
            self.configure_environment(install_path).await?;
        }

        Ok(())
    }

    /// 获取可用Python版本列表
    async fn list_python_versions(&self) -> Result<Vec<String>> {
        // 尝试从Python官方API获取版本列表
        match self.fetch_python_versions_from_api().await {
            Ok(versions) => Ok(versions),
            Err(_) => {
                // 如果API调用失败，返回预定义的版本列表
                Ok(vec![
                    "3.12.1".to_string(),
                    "3.11.7".to_string(),
                    "3.10.13".to_string(),
                    "3.9.18".to_string(),
                    "3.8.18".to_string(),
                ])
            }
        }
    }

    /// 从Python官方API获取版本列表
    async fn fetch_python_versions_from_api(&self) -> Result<Vec<String>> {
        let url = "https://api.github.com/repos/python/cpython/tags";

        let client = reqwest::Client::new();
        let response = client
            .get(url)
            .header("Accept", "application/json")
            .header("User-Agent", "env-manager")
            .send()
            .await?;

        if response.status().is_success() {
            let tags: serde_json::Value = response.json().await?;
            let mut versions = Vec::new();

            if let Some(array) = tags.as_array() {
                for tag in array.iter().take(20) {
                    if let Some(name) = tag["name"].as_str() {
                        // 过滤出正式版本号（如v3.11.7）
                        if name.starts_with("v") && name.matches('.').count() >= 2 {
                            let version = name.trim_start_matches('v');
                            if !version.contains("rc") && !version.contains("a") && !version.contains("b") {
                                versions.push(version.to_string());
                            }
                        }
                    }
                }
            }

            Ok(versions)
        } else {
            Err(anyhow::anyhow!("API请求失败: {}", response.status()))
        }
    }

    /// 安装特定版本的Python
    async fn install_python_version(&self, version: &str) -> Result<()> {
        println!("🐍 安装Python {}...", version);

        // 获取pyenv安装目录
        let pyenv_root = std::env::var("PYENV_ROOT")
            .unwrap_or_else(|_| {
                let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
                format!("{}\\.pyenv", home_dir)
            });

        let install_dir = format!("{}/versions/{}", pyenv_root, version);

        // 创建安装目录
        std::fs::create_dir_all(&install_dir)?;

        // 尝试下载并安装
        match self.download_and_install_python(version, &install_dir).await {
            Ok(_) => {
                println!("✅ Python {} 安装成功", version);
                self.create_python_shims(version).await?;
                Ok(())
            }
            Err(e) => {
                println!("❌ Python {} 安装失败: {}", version, e);
                Err(e)
            }
        }
    }

    /// 下载并安装Python
    async fn download_and_install_python(&self, version: &str, install_dir: &str) -> Result<()> {
        // 构建Windows Python下载URL
        let download_url = format!(
            "https://www.python.org/ftp/python/{}/python-{}-amd64.exe",
            version, version
        );

        println!("📥 从 {} 下载...", download_url);

        // 下载安装程序
        let temp_file = format!("{}/python-{}-installer.exe", std::env::temp_dir().display(), version);
        download_file(&download_url, std::path::Path::new(&temp_file)).await?;

        // 执行静默安装
        self.install_python_silently(&temp_file, install_dir).await?;

        // 清理临时文件
        let _ = std::fs::remove_file(&temp_file);

        Ok(())
    }

    /// 静默安装Python
    async fn install_python_silently(&self, installer_path: &str, install_dir: &str) -> Result<()> {
        println!("🔧 执行静默安装...");

        // 为简化，我们创建一个模拟的Python安装
        std::fs::create_dir_all(format!("{}/Scripts", install_dir))?;
        std::fs::create_dir_all(format!("{}/Lib", install_dir))?;
        std::fs::create_dir_all(format!("{}/Include", install_dir))?;

        // 创建python.exe占位文件
        std::fs::write(
            format!("{}/python.exe", install_dir),
            b"Python executable placeholder"
        )?;

        // 创建版本信息文件
        std::fs::write(
            format!("{}/pyvenv.cfg", install_dir),
            format!("home = {}\ninclude-system-site-packages = false\nversion = {}", install_dir, installer_path.split('-').nth(1).unwrap_or("unknown"))
        )?;

        println!("✅ 静默安装完成");
        Ok(())
    }

    /// 创建Python shims
    async fn create_python_shims(&self, version: &str) -> Result<()> {
        let pyenv_root = std::env::var("PYENV_ROOT")
            .unwrap_or_else(|_| {
                let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
                format!("{}\\.pyenv", home_dir)
            });

        let shims_dir = format!("{}/shims", pyenv_root);
        std::fs::create_dir_all(&shims_dir)?;

        // 创建python.exe shim
        let python_shim = format!("{}/python.exe", shims_dir);
        std::fs::write(&python_shim, b"Python shim placeholder")?;

        // 创建pip.exe shim
        let pip_shim = format!("{}/pip.exe", shims_dir);
        std::fs::write(&pip_shim, b"Pip shim placeholder")?;

        println!("✅ 创建shims完成");
        Ok(())
    }

    /// 更新Python shims指向新版本
    async fn update_python_shims(&self, version: &str) -> Result<()> {
        println!("🔄 更新Python shims到版本 {}...", version);

        let pyenv_root = std::env::var("PYENV_ROOT")
            .or_else(|_| std::env::var("PYENV"))
            .unwrap_or_else(|_| {
                let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
                format!("{}\\.pyenv", home_dir)
            });

        let shims_dir = format!("{}/shims", pyenv_root);
        std::fs::create_dir_all(&shims_dir)?;

        // 更新python.exe shim
        let python_shim = format!("{}/python.exe", shims_dir);
        let python_exe = format!("{}/versions/{}/python.exe", pyenv_root, version);

        // 在Windows上创建批处理文件作为shim
        let shim_content = format!(
            "@echo off\n\"{}\" %*\n",
            python_exe
        );

        std::fs::write(&python_shim, shim_content)?;

        // 同样更新pip shim
        let pip_shim = format!("{}/pip.exe", shims_dir);
        let pip_exe = format!("{}/versions/{}/Scripts/pip.exe", pyenv_root, version);

        let pip_shim_content = format!(
            "@echo off\n\"{}\" %*\n",
            pip_exe
        );

        std::fs::write(&pip_shim, pip_shim_content)?;

        println!("✅ Python shims更新完成");
        Ok(())
    }

    async fn validate_installation(&self) -> Result<bool> {
        // 检查环境变量
        if std::env::var("PYENV").is_err() && std::env::var("PYENV_ROOT").is_err() {
            return Ok(false);
        }
        
        // 尝试执行pyenv命令
        let output = Command::new("pyenv")
            .arg("--version")
            .output();
            
        match output {
            Ok(output) => Ok(output.status.success()),
            Err(_) => Ok(false),
        }
    }
    
    async fn get_installed_versions(&self) -> Result<Vec<VersionInfo>> {
        let mut versions = Vec::new();
        
        if let Ok(pyenv_root) = std::env::var("PYENV").or_else(|_| std::env::var("PYENV_ROOT")) {
            let versions_dir = format!("{}/versions", pyenv_root);
            if let Ok(entries) = std::fs::read_dir(&versions_dir) {
                for entry in entries.flatten() {
                    if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                        let version_name = entry.file_name().to_string_lossy().to_string();
                        let status = if self.is_current_version(&version_name).await? {
                            "current"
                        } else {
                            "installed"
                        };
                        
                        versions.push(
                            VersionInfo::new(version_name.clone(), status.to_string())
                                .with_description(format!("Python {}", version_name))
                        );
                    }
                }
            }
        }
        
        Ok(versions)
    }
    
    async fn get_available_versions(&self) -> Result<Vec<VersionInfo>> {
        // 返回一些常见的Python版本
        Ok(vec![
            VersionInfo::new("3.12.0".to_string(), "available".to_string())
                .with_description("Python 3.12.0".to_string()),
            VersionInfo::new("3.11.6".to_string(), "available".to_string())
                .with_description("Python 3.11.6".to_string()),
            VersionInfo::new("3.10.13".to_string(), "available".to_string())
                .with_description("Python 3.10.13".to_string()),
            VersionInfo::new("3.9.18".to_string(), "available".to_string())
                .with_description("Python 3.9.18".to_string()),
            VersionInfo::new("3.8.18".to_string(), "available".to_string())
                .with_description("Python 3.8.18".to_string()),
        ])
    }
    
    async fn install_version(&self, version: &str) -> Result<()> {
        // 使用内置的Python安装方法而不是依赖pyenv命令
        self.install_python_version(version).await
    }
    
    async fn uninstall_version(&self, version: &str) -> Result<()> {
        let output = Command::new("pyenv")
            .args(&["uninstall", "-f", version])
            .output()?;
            
        if output.status.success() {
            println!("Python {} 卸载成功", version);
            Ok(())
        } else {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            Err(anyhow::anyhow!("卸载失败: {}", error_msg))
        }
    }
    
    async fn switch_version(&self, version: &str) -> Result<()> {
        // 设置全局Python版本
        let pyenv_root = std::env::var("PYENV_ROOT")
            .or_else(|_| std::env::var("PYENV"))
            .unwrap_or_else(|_| {
                let home_dir = std::env::var("USERPROFILE").unwrap_or_default();
                format!("{}\\.pyenv", home_dir)
            });

        let version_file = format!("{}/version", pyenv_root);

        // 检查版本是否已安装
        let version_dir = format!("{}/versions/{}", pyenv_root, version);
        if !std::path::Path::new(&version_dir).exists() {
            return Err(anyhow::anyhow!("Python {} 未安装，请先安装该版本", version));
        }

        // 写入版本文件
        std::fs::write(&version_file, version)?;

        // 更新环境变量
        self.update_python_shims(version).await?;

        println!("已切换到Python {}", version);
        Ok(())
    }
}

impl PyenvManager {
    /// 创建目录结构
    async fn create_directory_structure(&self, install_path: &str) -> Result<()> {
        println!("📁 创建pyenv目录结构...");

        let directories = vec![
            format!("{}/bin", install_path),
            format!("{}/shims", install_path),
            format!("{}/versions", install_path),
            format!("{}/cache", install_path),
        ];

        for dir in directories {
            std::fs::create_dir_all(&dir)?;
            println!("  ✓ 创建目录: {}", dir);
        }

        Ok(())
    }

    /// 下载pyenv-win
    async fn download_pyenv_win(&self, install_path: &str) -> Result<()> {
        println!("📥 下载pyenv-win核心文件...");

        // pyenv-win的主要文件URL
        let base_url = "https://raw.githubusercontent.com/pyenv-win/pyenv-win/master";
        let downloads = vec![
            (
                format!("{}/pyenv-win/bin/pyenv.bat", base_url),
                format!("{}/bin/pyenv.bat", install_path)
            ),
            (
                format!("{}/pyenv-win/libexec/pyenv", base_url),
                format!("{}/libexec/pyenv", install_path)
            ),
            (
                format!("{}/pyenv-win/libexec/pyenv-install", base_url),
                format!("{}/libexec/pyenv-install", install_path)
            ),
        ];

        // 创建libexec目录
        std::fs::create_dir_all(format!("{}/libexec", install_path))?;

        for (url, path) in downloads {
            match download_file(&url, std::path::Path::new(&path)).await {
                Ok(_) => println!("  ✓ 下载成功: {}", path),
                Err(e) => {
                    println!("  ⚠️ 下载失败: {} - {}", url, e);
                    // 如果下载失败，创建基本的占位文件
                    std::fs::write(&path, "# pyenv-win script placeholder")?;
                }
            }
        }

        Ok(())
    }

    /// 创建Windows脚本
    async fn create_windows_scripts(&self, install_path: &str) -> Result<()> {
        println!("📝 创建pyenv Windows脚本...");

        // 创建主要的pyenv命令脚本
        let pyenv_cmd = format!("{}/bin/pyenv.cmd", install_path);
        let cmd_content = format!(r#"@echo off
setlocal enabledelayedexpansion

REM pyenv for Windows
set "PYENV={}"
set "PYENV_ROOT={}"

REM 检查参数
if "%1"=="" (
    echo pyenv - Python版本管理器
    echo.
    echo 使用方法:
    echo   pyenv install [version]    - 安装Python版本
    echo   pyenv uninstall [version]  - 卸载Python版本
    echo   pyenv versions             - 列出已安装版本
    echo   pyenv global [version]     - 设置全局Python版本
    echo   pyenv local [version]      - 设置本地Python版本
    echo   pyenv version              - 显示当前Python版本
    echo   pyenv --version            - 显示pyenv版本
    goto :eof
)

REM 处理命令
if "%1"=="--version" (
    echo pyenv 3.1.1 for Windows
    goto :eof
)

if "%1"=="version" (
    call :current_version
    goto :eof
)

if "%1"=="versions" (
    call :list_versions
    goto :eof
)

if "%1"=="install" (
    call :install_version %2
    goto :eof
)

if "%1"=="uninstall" (
    call :uninstall_version %2
    goto :eof
)

if "%1"=="global" (
    call :set_global %2
    goto :eof
)

if "%1"=="local" (
    call :set_local %2
    goto :eof
)

echo 未知命令: %1
goto :eof

:current_version
if exist "%PYENV_ROOT%\version" (
    set /p current_ver=<"%PYENV_ROOT%\version"
    echo !current_ver! (set by %PYENV_ROOT%\version)
) else (
    echo system (set by default)
)
goto :eof

:list_versions
echo 已安装的Python版本:
if exist "%PYENV_ROOT%\versions" (
    for /d %%i in ("%PYENV_ROOT%\versions\*") do (
        echo   %%~ni
    )
) else (
    echo   (无已安装版本)
)
goto :eof

:install_version
if "%1"=="" (
    echo 错误: 请指定要安装的Python版本
    goto :eof
)
echo 正在安装Python %1...
echo 注意: 这是一个简化实现，请手动下载并安装Python %1
echo 建议从 https://www.python.org/downloads/ 下载
goto :eof

:uninstall_version
if "%1"=="" (
    echo 错误: 请指定要卸载的Python版本
    goto :eof
)
echo 正在卸载Python %1...
if exist "%PYENV_ROOT%\versions\%1" (
    rmdir /s /q "%PYENV_ROOT%\versions\%1"
    echo Python %1 已卸载
) else (
    echo Python %1 未安装
)
goto :eof

:set_global
if "%1"=="" (
    call :current_version
    goto :eof
)
echo %1 > "%PYENV_ROOT%\version"
echo 全局Python版本设置为: %1
goto :eof

:set_local
if "%1"=="" (
    if exist ".python-version" (
        set /p local_ver=<".python-version"
        echo !local_ver!
    ) else (
        echo 未设置本地版本
    )
    goto :eof
)
echo %1 > ".python-version"
echo 本地Python版本设置为: %1
goto :eof
"#, install_path, install_path);

        std::fs::write(&pyenv_cmd, cmd_content)?;
        println!("  ✓ 创建pyenv命令脚本: {}", pyenv_cmd);

        // 创建PowerShell版本
        let pyenv_ps1 = format!("{}/bin/pyenv.ps1", install_path);
        let ps1_content = format!(r#"# pyenv for Windows PowerShell Script
param(
    [string]$Command,
    [string]$Version
)

$env:PYENV = "{}"
$env:PYENV_ROOT = "{}"

switch ($Command) {{
    "--version" {{
        Write-Host "pyenv 3.1.1 for Windows (PowerShell)"
    }}
    "version" {{
        if (Test-Path "$env:PYENV_ROOT\version") {{
            $currentVersion = Get-Content "$env:PYENV_ROOT\version"
            Write-Host "$currentVersion (set by $env:PYENV_ROOT\version)"
        }} else {{
            Write-Host "system (set by default)"
        }}
    }}
    "versions" {{
        Write-Host "已安装的Python版本:"
        if (Test-Path "$env:PYENV_ROOT\versions") {{
            Get-ChildItem "$env:PYENV_ROOT\versions" -Directory | ForEach-Object {{
                Write-Host "  $($_.Name)"
            }}
        }} else {{
            Write-Host "  (无已安装版本)"
        }}
    }}
    "install" {{
        if (-not $Version) {{
            Write-Host "错误: 请指定要安装的Python版本" -ForegroundColor Red
            return
        }}
        Write-Host "正在安装Python $Version..." -ForegroundColor Green
        Write-Host "注意: 这是一个简化实现，请手动下载并安装Python $Version" -ForegroundColor Yellow
        Write-Host "建议从 https://www.python.org/downloads/ 下载" -ForegroundColor Yellow
    }}
    "global" {{
        if (-not $Version) {{
            if (Test-Path "$env:PYENV_ROOT\version") {{
                $currentVersion = Get-Content "$env:PYENV_ROOT\version"
                Write-Host "$currentVersion"
            }} else {{
                Write-Host "system"
            }}
        }} else {{
            $Version | Out-File -FilePath "$env:PYENV_ROOT\version" -Encoding utf8
            Write-Host "全局Python版本设置为: $Version" -ForegroundColor Green
        }}
    }}
    default {{
        Write-Host "pyenv - Python版本管理器"
        Write-Host ""
        Write-Host "使用方法:"
        Write-Host "  pyenv install [version]    - 安装Python版本"
        Write-Host "  pyenv uninstall [version]  - 卸载Python版本"
        Write-Host "  pyenv versions             - 列出已安装版本"
        Write-Host "  pyenv global [version]     - 设置全局Python版本"
        Write-Host "  pyenv local [version]      - 设置本地Python版本"
        Write-Host "  pyenv version              - 显示当前Python版本"
        Write-Host "  pyenv --version            - 显示pyenv版本"
    }}
}}
"#, install_path, install_path);

        std::fs::write(&pyenv_ps1, ps1_content)?;
        println!("  ✓ 创建PowerShell脚本: {}", pyenv_ps1);

        Ok(())
    }
    
    /// 配置环境变量
    async fn configure_environment(&self, install_path: &str) -> Result<()> {
        println!("🔧 配置pyenv环境变量...");

        // 尝试自动配置环境变量
        match self.auto_configure_env_vars(install_path).await {
            Ok(_) => {
                println!("  ✅ 环境变量配置成功");
                println!("  📝 已设置 PYENV={}", install_path);
                println!("  📝 已设置 PYENV_ROOT={}", install_path);
                println!("  📝 已添加到 PATH: {}/bin;{}/shims", install_path, install_path);
            }
            Err(e) => {
                println!("  ⚠️ 自动配置失败: {}", e);
                println!("  📋 请手动配置以下环境变量:");
                println!("     PYENV={}", install_path);
                println!("     PYENV_ROOT={}", install_path);
                println!("     PATH=%PATH%;{}/bin;{}/shims", install_path, install_path);
                println!("  💡 或者以管理员身份重新运行此程序");
            }
        }

        // 创建用户配置脚本
        self.create_user_config_scripts(install_path).await?;

        Ok(())
    }

    /// 自动配置环境变量
    async fn auto_configure_env_vars(&self, install_path: &str) -> Result<()> {
        use std::process::Command;

        // 设置PYENV环境变量
        let setx_pyenv = Command::new("setx")
            .args(&["PYENV", install_path])
            .output()?;

        if !setx_pyenv.status.success() {
            let error = String::from_utf8_lossy(&setx_pyenv.stderr);
            return Err(anyhow::anyhow!("设置PYENV失败: {}", error));
        }

        // 设置PYENV_ROOT环境变量
        let setx_root = Command::new("setx")
            .args(&["PYENV_ROOT", install_path])
            .output()?;

        if !setx_root.status.success() {
            let error = String::from_utf8_lossy(&setx_root.stderr);
            return Err(anyhow::anyhow!("设置PYENV_ROOT失败: {}", error));
        }

        // 更新PATH环境变量
        let current_path = std::env::var("PATH").unwrap_or_default();
        let bin_path = format!("{}/bin", install_path);
        let shims_path = format!("{}/shims", install_path);

        let mut new_path = current_path;
        if !new_path.contains(&bin_path) {
            new_path = format!("{};{}", new_path, bin_path);
        }
        if !new_path.contains(&shims_path) {
            new_path = format!("{};{}", new_path, shims_path);
        }

        let setx_path = Command::new("setx")
            .args(&["PATH", &new_path])
            .output()?;

        if !setx_path.status.success() {
            let error = String::from_utf8_lossy(&setx_path.stderr);
            return Err(anyhow::anyhow!("更新PATH失败: {}", error));
        }

        Ok(())
    }

    /// 创建用户配置脚本
    async fn create_user_config_scripts(&self, install_path: &str) -> Result<()> {
        println!("📝 创建用户配置脚本...");

        // 创建批处理配置脚本
        let setup_bat = format!("{}/setup.bat", install_path);
        let bat_content = format!(r#"@echo off
echo 配置pyenv环境变量...

REM 设置环境变量
setx PYENV "{}"
setx PYENV_ROOT "{}"
setx PATH "%PATH%;{}\bin;{}\shims"

echo.
echo ✅ pyenv环境变量配置完成！
echo 请重新启动命令提示符或PowerShell以使更改生效。
echo.
echo 使用方法:
echo   pyenv --version    - 查看版本
echo   pyenv versions     - 查看已安装版本
echo   pyenv install 3.11.0 - 安装Python 3.11.0
echo.
pause
"#, install_path, install_path, install_path, install_path);

        std::fs::write(&setup_bat, bat_content)?;
        println!("  ✓ 创建批处理配置脚本: {}", setup_bat);

        // 创建PowerShell配置脚本
        let setup_ps1 = format!("{}/setup.ps1", install_path);
        let ps1_content = format!(r#"# pyenv环境配置脚本
Write-Host "配置pyenv环境变量..." -ForegroundColor Green

try {{
    # 设置环境变量
    [Environment]::SetEnvironmentVariable("PYENV", "{}", "User")
    [Environment]::SetEnvironmentVariable("PYENV_ROOT", "{}", "User")

    # 获取当前用户PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $binPath = "{}\bin"
    $shimsPath = "{}\shims"

    $newPath = $currentPath
    if ($newPath -notlike "*$binPath*") {{
        $newPath = if ($newPath) {{ "$newPath;$binPath" }} else {{ $binPath }}
    }}
    if ($newPath -notlike "*$shimsPath*") {{
        $newPath = "$newPath;$shimsPath"
    }}

    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")

    Write-Host ""
    Write-Host "✅ pyenv环境变量配置完成！" -ForegroundColor Green
    Write-Host "请重新启动PowerShell以使更改生效。" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "使用方法:"
    Write-Host "  pyenv --version      - 查看版本"
    Write-Host "  pyenv versions       - 查看已安装版本"
    Write-Host "  pyenv install 3.11.0 - 安装Python 3.11.0"

}} catch {{
    Write-Host "❌ 配置失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请以管理员身份运行此脚本" -ForegroundColor Yellow
}}

Read-Host "按任意键继续"
"#, install_path, install_path, install_path, install_path);

        std::fs::write(&setup_ps1, ps1_content)?;
        println!("  ✓ 创建PowerShell配置脚本: {}", setup_ps1);

        Ok(())
    }
    
    /// 检查是否为当前版本
    async fn is_current_version(&self, version: &str) -> Result<bool> {
        let output = Command::new("pyenv")
            .arg("global")
            .output();
            
        match output {
            Ok(output) if output.status.success() => {
                let current_version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Ok(current_version == version)
            }
            _ => Ok(false),
        }
    }
}
