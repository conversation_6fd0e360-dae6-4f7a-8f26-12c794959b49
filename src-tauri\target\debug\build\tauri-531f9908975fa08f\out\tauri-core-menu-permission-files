["\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\G:\\code\\my-tool\\env-manager\\src-tauri\\target\\debug\\build\\tauri-531f9908975fa08f\\out\\permissions\\menu\\autogenerated\\default.toml"]