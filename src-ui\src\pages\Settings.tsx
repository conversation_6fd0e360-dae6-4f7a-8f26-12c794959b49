import React, { useState } from 'react';
import { Typography, Card, Form, Input, Switch, Button, Space, Divider, Select, Row, Col, Alert } from 'antd';
import { SettingOutlined, SaveOutlined, ReloadOutlined, FolderOutlined, WarningOutlined } from '@ant-design/icons';
import SystemInfo from '../components/business/SystemInfo';
import FileSelector from '../components/common/FileSelector';

const { Title, Paragraph } = Typography;
const { Option } = Select;

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [defaultInstallPath, setDefaultInstallPath] = useState<string>('');
  const [backupPaths, setBackupPaths] = useState<string[]>([]);
  const [configFiles, setConfigFiles] = useState<string[]>([]);
  const [allowNvmPopup, setAllowNvmPopup] = useState<boolean>(false);

  const onFinish = (values: any) => {
    console.log('保存设置:', values);
  };

  const onReset = () => {
    form.resetFields();
  };

  return (
    <div style={{
      height: '100%',
      overflow: 'auto',
      padding: '0 8px' // 减少左右padding，为滚动条留出空间
    }}>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card
        title={
          <Space>
            <SettingOutlined />
            <span>应用设置</span>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            defaultInstallPath: '',
            autoConfigureEnv: true,
            checkUpdates: true,
            theme: 'light',
            language: 'zh-CN',
          }}
        >
          <Title level={4}>安装设置</Title>
          
          <Form.Item
            label="默认安装路径"
            help="选择工具的默认安装目录"
          >
            <FileSelector
              mode="directory"
              value={defaultInstallPath}
              onChange={(value) => {
                const path = Array.isArray(value) ? value[0] : value || '';
                setDefaultInstallPath(path);
                form.setFieldValue('defaultInstallPath', path);
              }}
              placeholder="选择默认安装目录"
              buttonText="选择目录"
              buttonIcon={<FolderOutlined />}
              validator={(paths) => {
                if (paths.length === 0) return null;
                const path = paths[0];
                if (path.includes('Program Files') || path.includes('Windows')) {
                  return '不建议选择系统目录作为安装路径';
                }
                return null;
              }}
            />
          </Form.Item>

          <Form.Item
            label="自动配置环境变量"
            name="autoConfigureEnv"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Title level={4}>备份和配置</Title>

          <Form.Item
            label="备份目录"
            help="选择多个目录用于备份配置文件"
          >
            <FileSelector
              mode="directory"
              multiple={true}
              value={backupPaths}
              onChange={(value) => {
                const paths = Array.isArray(value) ? value : value ? [value] : [];
                setBackupPaths(paths);
              }}
              placeholder="选择备份目录"
              buttonText="添加备份目录"
              maxItems={5}
              showPreview={true}
            />
          </Form.Item>

          <Form.Item
            label="配置文件"
            help="选择要管理的配置文件"
          >
            <FileSelector
              mode="file"
              multiple={true}
              value={configFiles}
              onChange={(value) => {
                const files = Array.isArray(value) ? value : value ? [value] : [];
                setConfigFiles(files);
              }}
              placeholder="选择配置文件"
              buttonText="添加配置文件"
              filters={[
                { name: '配置文件', extensions: ['json', 'yaml', 'yml', 'toml', 'ini', 'conf'] },
                { name: 'JSON文件', extensions: ['json'] },
                { name: 'YAML文件', extensions: ['yaml', 'yml'] },
                { name: '所有文件', extensions: ['*'] }
              ]}
              maxItems={10}
              showPreview={true}
              validator={(files) => {
                const invalidFiles = files.filter(file =>
                  !file.match(/\.(json|yaml|yml|toml|ini|conf)$/i)
                );
                if (invalidFiles.length > 0) {
                  return `以下文件类型不支持: ${invalidFiles.map(f => f.split('.').pop()).join(', ')}`;
                }
                return null;
              }}
            />
          </Form.Item>

          <Divider />

          <Title level={4}>应用设置</Title>

          <Form.Item
            label="自动检查更新"
            name="checkUpdates"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="主题"
            name="theme"
          >
            <Select>
              <Option value="light">浅色</Option>
              <Option value="dark">深色</Option>
              <Option value="auto">跟随系统</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="语言"
            name="language"
          >
            <Select>
              <Option value="zh-CN">简体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>

          <Divider />

          {/* NVM 设置 */}
          <Card title="NVM 设置" size="small" style={{ marginBottom: 16 }}>
            <Alert
              message="NVM 命令执行设置"
              description="NVM for Windows 需要在特定环境中运行。启用此选项将允许执行可能显示弹窗的 NVM 命令，但可能会影响用户体验。"
              type="warning"
              icon={<WarningOutlined />}
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form.Item
              label="允许 NVM 命令弹窗"
              name="allowNvmPopup"
              valuePropName="checked"
              tooltip="启用后，可以执行完整的 NVM 命令（如安装、切换版本），但可能会显示系统弹窗"
            >
              <Switch
                checked={allowNvmPopup}
                onChange={setAllowNvmPopup}
                checkedChildren="允许"
                unCheckedChildren="禁止"
              />
            </Form.Item>

            {allowNvmPopup && (
              <Alert
                message="已启用 NVM 命令弹窗"
                description="现在可以执行完整的 NVM 功能，包括安装和切换 Node.js 版本。某些操作可能会显示系统弹窗。"
                type="info"
                showIcon
                style={{ marginTop: 8 }}
              />
            )}
          </Card>

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                保存设置
              </Button>
              <Button onClick={onReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
        </Col>

        <Col xs={24} lg={8}>
          <SystemInfo />
        </Col>
      </Row>
    </div>
  );
};

export default Settings;
