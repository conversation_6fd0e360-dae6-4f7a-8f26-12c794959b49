{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 11933756421952101080, "profile": 17672942494452627365, "path": 5265518918338594008, "deps": [[1630737303963722877, "tauri_plugin_dialog", false, 1722927789755282137], [3533833876439050747, "version_compare", false, 7987293048280734241], [3722963349756955755, "once_cell", false, 8207665967349426210], [4352886507220678900, "serde_json", false, 1059408172161946174], [4707735785701411121, "tauri_plugin_window_state", false, 16730768576360563102], [4716821492401257289, "zip", false, 5690908249374365085], [6079318424673677659, "sha256", false, 401354046676284217], [7244058819997729774, "reqwest", false, 10632953332690148450], [8008191657135824715, "thiserror", false, 2982668330080162942], [9451456094439810778, "regex", false, 1757113780276834260], [9689903380558560274, "serde", false, 10535885796594175147], [9857275760291862238, "sha2", false, 11148134008726955128], [9897246384292347999, "chrono", false, 7493563776841235036], [10629569228670356391, "futures_util", false, 10290085434348422562], [11207653606310558077, "anyhow", false, 8094606839429439598], [12400935292645517134, "build_script_build", false, 4621258996128799145], [14285978758320820277, "tauri_plugin_fs", false, 16529052113654198933], [15020664462999436276, "tauri", false, 11452429128793777050], [15609422047640926750, "toml", false, 15758916459479234189], [16429266147849286097, "tauri_plugin_opener", false, 9794837596613086346], [16611674984963787466, "async_trait", false, 18398624175857034335], [16928111194414003569, "dirs", false, 13722319158371109550], [17121285809968213651, "md5", false, 15395892219173009032], [17531218394775549125, "tokio", false, 17060898677026264165], [17772299992546037086, "flate2", false, 8417525359862951576]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\env-manager-1b80c13050594898\\dep-lib-env_manager_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}